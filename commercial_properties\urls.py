from django.urls import path
from . import views

app_name = 'commercial_properties'

urlpatterns = [
    path('', views.CommercialPropertyListView.as_view(), name='list'),
    path('add/', views.CommercialPropertyCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', views.CommercialPropertyUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.CommercialPropertyDeleteView.as_view(), name='delete'),
    path('overdue/', views.OverdueCommercialPropertyListView.as_view(), name='overdue'),
    path('payment-history/', views.PaymentHistoryListView.as_view(), name='payment_history'),
    path('<int:pk>/renew/', views.RenewCommercialPropertyView.as_view(), name='renew'),
    path('export/', views.ExportCommercialPropertyView.as_view(), name='export'),
    path('import/', views.ImportCommercialPropertyView.as_view(), name='import'),
]
