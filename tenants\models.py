from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal


class Tenant(models.Model):
    """租客模型"""
    # 基本信息
    building_number = models.CharField('楼号', max_length=10)
    room_number = models.CharField('房号', max_length=10)
    area = models.DecimalField('平米数', max_digits=8, decimal_places=2)
    tenant_name = models.CharField('租客姓名', max_length=50)
    id_card = models.CharField('身份证号', max_length=18)
    resident_count = models.IntegerField('租住人数', default=1)
    landlord_name = models.CharField('房东姓名', max_length=50)
    landlord_phone = models.CharField('房东电话', max_length=20)

    # 租住信息
    move_in_date = models.DateField('入住时间')
    property_fee_due_date = models.DateField('物业费到期时间')
    property_fee = models.DecimalField('物业费用', max_digits=10, decimal_places=2)
    floor = models.IntegerField('楼层', default=1)

    # 状态
    STATUS_CHOICES = [
        ('active', '正常'),
        ('overdue', '逾期'),
        ('checkout', '已退房'),
    ]
    status = models.CharField('状态', max_length=10, choices=STATUS_CHOICES, default='active')

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '租客'
        verbose_name_plural = '租客'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.building_number}-{self.room_number} {self.tenant_name}"

    @property
    def days_until_due(self):
        """距离到期天数"""
        if self.property_fee_due_date:
            delta = self.property_fee_due_date - timezone.now().date()
            return delta.days
        return 0

    @property
    def is_overdue(self):
        """是否逾期"""
        return self.days_until_due < 0

    @property
    def overdue_days(self):
        """逾期天数"""
        if self.is_overdue:
            return abs(self.days_until_due)
        return 0

    def calculate_property_fee(self):
        """计算物业费"""
        if not self.area or not self.floor:
            return Decimal('0.00')

        # 基础费用：每平米1元
        base_fee = self.area * Decimal('1.00')

        # 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
        if self.floor <= 11:
            elevator_fee = self.area * Decimal('0.30')
        else:
            elevator_fee = self.area * Decimal('0.35')

        total_fee = base_fee + elevator_fee
        return total_fee.quantize(Decimal('0.01'))

    def save(self, *args, **kwargs):
        # 自动计算物业费
        if self.area and self.floor:
            self.property_fee = self.calculate_property_fee()

        # 根据房号自动设置平米数
        if self.room_number and not self.area:
            last_two = self.room_number[-2:] if len(self.room_number) >= 2 else ''
            if last_two in ['01', '04']:
                self.area = Decimal('130.00')
            elif last_two in ['02', '03']:
                self.area = Decimal('90.00')

        # 更新状态
        if self.property_fee_due_date:
            if self.is_overdue and self.status == 'active':
                self.status = 'overdue'
            elif not self.is_overdue and self.status == 'overdue':
                self.status = 'active'

        super().save(*args, **kwargs)


class TenantPaymentHistory(models.Model):
    """租客缴费流水"""
    tenant = models.ForeignKey(Tenant, on_delete=models.CASCADE, verbose_name='租客')
    payment_date = models.DateTimeField('交费时间', default=timezone.now)
    amount = models.DecimalField('缴费金额', max_digits=10, decimal_places=2)
    fee_start_date = models.DateField('物业费开始时间')
    fee_end_date = models.DateField('物业费结束时间')
    payment_method = models.CharField('缴费方式', max_length=20, default='现金')
    notes = models.TextField('备注', blank=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '租客缴费记录'
        verbose_name_plural = '租客缴费记录'
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.tenant} - ¥{self.amount} - {self.payment_date.strftime('%Y-%m-%d')}"
