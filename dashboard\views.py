from django.shortcuts import render
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Count, Sum
from datetime import datetime, timedelta
from django.utils import timezone


class IndexView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard/index.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 这里先设置一些模拟数据，后面会替换为真实数据
        context.update({
            'total_tenants': 156,
            'total_commercial_properties': 89,
            'total_shops': 23,
            'total_parking_spaces': 67,
            'overdue_tenants': 12,
            'overdue_commercial': 8,
            'overdue_shops': 3,
            'overdue_parking': 5,
            'monthly_income': 125680.50,
            'yearly_income': 1508166.00,
            'recent_payments': [
                {
                    'type': '租客',
                    'name': '张三',
                    'room': '1-101',
                    'amount': 450.00,
                    'date': timezone.now() - timedelta(hours=2),
                },
                {
                    'type': '商品房',
                    'name': '李四',
                    'room': '2-205',
                    'amount': 380.00,
                    'date': timezone.now() - timedelta(hours=5),
                },
                {
                    'type': '商铺',
                    'name': '王五',
                    'room': 'S-01',
                    'amount': 750.00,
                    'date': timezone.now() - timedelta(days=1),
                },
            ],
            'upcoming_expiries': [
                {
                    'type': '租客',
                    'name': '赵六',
                    'room': '3-301',
                    'expire_date': timezone.now() + timedelta(days=3),
                },
                {
                    'type': '车位',
                    'name': '钱七',
                    'room': 'P-A15',
                    'expire_date': timezone.now() + timedelta(days=7),
                },
            ]
        })

        return context
