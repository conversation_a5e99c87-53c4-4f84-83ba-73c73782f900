{% extends 'base/base.html' %}
{% load static %}

{% block title %}首页 - 东悦物业管理系统{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -15px 2rem -15px;
        border-radius: 0 0 20px 20px;
    }
    
    .welcome-text {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .current-time {
        font-size: 1rem;
        opacity: 0.9;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #667eea, #764ba2);
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: #666;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .stats-change {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .stats-change.positive {
        color: #28a745;
    }
    
    .stats-change.negative {
        color: #dc3545;
    }
    
    .icon-tenants { background: linear-gradient(135deg, #667eea, #764ba2); }
    .icon-commercial { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .icon-shops { background: linear-gradient(135deg, #4facfe, #00f2fe); }
    .icon-parking { background: linear-gradient(135deg, #43e97b, #38f9d7); }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-top: 2rem;
    }
    
    .dashboard-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    
    .payment-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .payment-item:last-child {
        border-bottom: none;
    }
    
    .payment-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 1rem;
    }
    
    .payment-info {
        flex: 1;
    }
    
    .payment-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .payment-details {
        font-size: 0.875rem;
        color: #666;
    }
    
    .payment-amount {
        font-weight: 700;
        color: #28a745;
        font-size: 1.1rem;
    }
    
    .expiry-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .expiry-item:last-child {
        border-bottom: none;
    }
    
    .expiry-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffc107;
        color: white;
        margin-right: 1rem;
    }
    
    .expiry-info {
        flex: 1;
    }
    
    .expiry-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 0.25rem;
    }
    
    .expiry-details {
        font-size: 0.875rem;
        color: #666;
    }
    
    .expiry-days {
        font-weight: 600;
        color: #dc3545;
        font-size: 0.875rem;
    }
    
    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .dashboard-header {
            margin: -1.5rem -15px 1rem -15px;
            padding: 1.5rem 0;
        }
        
        .welcome-text {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-8">
                <div class="welcome-text">
                    <i class="fas fa-sun me-2"></i>欢迎回来，{{ user.username }}
                </div>
                <div class="current-time">
                    <i class="fas fa-clock me-2"></i>
                    <span id="current-time"></span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-none d-md-block">
                    <i class="fas fa-building" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stats-card">
            <div class="stats-icon icon-tenants">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number">{{ total_tenants }}</div>
            <div class="stats-label">租客总数</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up me-1"></i>+5 本月新增
            </div>
        </div>
        
        <div class="stats-card">
            <div class="stats-icon icon-commercial">
                <i class="fas fa-home"></i>
            </div>
            <div class="stats-number">{{ total_commercial_properties }}</div>
            <div class="stats-label">商品房总数</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up me-1"></i>+2 本月新增
            </div>
        </div>
        
        <div class="stats-card">
            <div class="stats-icon icon-shops">
                <i class="fas fa-store"></i>
            </div>
            <div class="stats-number">{{ total_shops }}</div>
            <div class="stats-label">商铺总数</div>
            <div class="stats-change">
                <i class="fas fa-minus me-1"></i>无变化
            </div>
        </div>
        
        <div class="stats-card">
            <div class="stats-icon icon-parking">
                <i class="fas fa-car"></i>
            </div>
            <div class="stats-number">{{ total_parking_spaces }}</div>
            <div class="stats-label">车位总数</div>
            <div class="stats-change positive">
                <i class="fas fa-arrow-up me-1"></i>+3 本月新增
            </div>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="dashboard-grid">
        <!-- 最近收款记录 -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-money-bill-wave me-2"></i>最近收款记录
                </h3>
                <a href="#" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            
            {% for payment in recent_payments %}
            <div class="payment-item">
                <div class="payment-avatar" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                    {{ payment.name|first }}
                </div>
                <div class="payment-info">
                    <div class="payment-name">{{ payment.name }}</div>
                    <div class="payment-details">
                        {{ payment.type }} · {{ payment.room }} · {{ payment.date|timesince }}前
                    </div>
                </div>
                <div class="payment-amount">
                    ¥{{ payment.amount }}
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 即将到期提醒 -->
        <div class="dashboard-card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>即将到期
                </h3>
                <span class="badge bg-warning">{{ upcoming_expiries|length }}</span>
            </div>
            
            {% for expiry in upcoming_expiries %}
            <div class="expiry-item">
                <div class="expiry-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="expiry-info">
                    <div class="expiry-name">{{ expiry.name }}</div>
                    <div class="expiry-details">
                        {{ expiry.type }} · {{ expiry.room }}
                    </div>
                </div>
                <div class="expiry-days">
                    {{ expiry.expire_date|timeuntil }}后到期
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 实时时间显示
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            weekday: 'long'
        });
        document.getElementById('current-time').textContent = timeString;
    }
    
    // 初始化时间显示
    updateTime();
    setInterval(updateTime, 1000);
    
    // 统计卡片动画
    document.addEventListener('DOMContentLoaded', function() {
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
</script>
{% endblock %}
