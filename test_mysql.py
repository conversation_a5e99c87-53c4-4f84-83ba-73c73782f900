import MySQLdb

# 测试不同的密码组合
passwords = ['', 'root', '123456', 'password', 'mysql']

for password in passwords:
    try:
        print(f"尝试密码: '{password}'")
        conn = MySQLdb.connect(
            host='localhost',
            user='root',
            password=password,
            port=3306
        )
        print(f"成功连接MySQL，密码是: '{password}'")
        
        # 创建数据库
        cursor = conn.cursor()
        cursor.execute("CREATE DATABASE IF NOT EXISTS property_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("数据库 property_management 创建成功")
        
        cursor.close()
        conn.close()
        break
        
    except Exception as e:
        print(f"连接失败: {e}")
        continue
else:
    print("所有密码都尝试失败，请手动检查MySQL配置")
