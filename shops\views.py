from django.shortcuts import render
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse

# 临时占位符视图
class ShopListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("商铺列表")

class ShopCreateView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("商铺创建")

class ShopUpdateView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商铺编辑")

class ShopDeleteView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商铺删除")

class OverdueShopListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("逾期商铺列表")

class CheckoutShopListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("退房商铺列表")

class PaymentHistoryListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("商铺缴费流水")

class RenewShopView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商铺续费")

class CheckoutShopView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商铺退房")

class ExportShopView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("导出商铺数据")

class ImportShopView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("导入商铺数据")
