{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-home me-2"></i>{{ title }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- 基本信息 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-1"></i>基本信息
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="{{ form.building_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-building me-1"></i>楼号 *
                                    </label>
                                    {{ form.building_number }}
                                    {% if form.building_number.errors %}
                                        <div class="text-danger">{{ form.building_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.unit_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-door-open me-1"></i>单元号
                                    </label>
                                    {{ form.unit_number }}
                                    {% if form.unit_number.errors %}
                                        <div class="text-danger">{{ form.unit_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.room_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-home me-1"></i>房号 *
                                    </label>
                                    {{ form.room_number }}
                                    {% if form.room_number.errors %}
                                        <div class="text-danger">{{ form.room_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.area.id_for_label }}" class="form-label">
                                        <i class="fas fa-ruler-combined me-1"></i>平米数 *
                                    </label>
                                    {{ form.area }}
                                    {% if form.area.errors %}
                                        <div class="text-danger">{{ form.area.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.floor.id_for_label }}" class="form-label">
                                        <i class="fas fa-layer-group me-1"></i>楼层 *
                                    </label>
                                    {{ form.floor }}
                                    {% if form.floor.errors %}
                                        <div class="text-danger">{{ form.floor.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- 业主信息和附加设施 -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user me-1"></i>业主信息
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="{{ form.owner_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>业主姓名 *
                                    </label>
                                    {{ form.owner_name }}
                                    {% if form.owner_name.errors %}
                                        <div class="text-danger">{{ form.owner_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.owner_phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>业主电话 *
                                    </label>
                                    {{ form.owner_phone }}
                                    {% if form.owner_phone.errors %}
                                        <div class="text-danger">{{ form.owner_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <h6 class="text-primary mb-3 mt-4">
                                    <i class="fas fa-plus-circle me-1"></i>附加设施
                                </h6>
                                
                                <!-- 地下室 -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.has_basement }}
                                        <label class="form-check-label" for="{{ form.has_basement.id_for_label }}">
                                            <i class="fas fa-warehouse me-1"></i>地下室
                                        </label>
                                    </div>
                                    <div class="mt-2" id="basement_number_field" style="display: none;">
                                        <label for="{{ form.basement_number.id_for_label }}" class="form-label">地下室号</label>
                                        {{ form.basement_number }}
                                    </div>
                                </div>
                                
                                <!-- 车位 -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        {{ form.has_parking }}
                                        <label class="form-check-label" for="{{ form.has_parking.id_for_label }}">
                                            <i class="fas fa-car me-1"></i>车位
                                        </label>
                                    </div>
                                    <div class="mt-2" id="parking_number_field" style="display: none;">
                                        <label for="{{ form.parking_number.id_for_label }}" class="form-label">车位号</label>
                                        {{ form.parking_number }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.property_fee_due_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>物业费到期日期 *
                                    </label>
                                    {{ form.property_fee_due_date }}
                                    {% if form.property_fee_due_date.errors %}
                                        <div class="text-danger">{{ form.property_fee_due_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-calculator me-1"></i>年物业费用（自动计算）
                                    </label>
                                    <input type="text" class="form-control" id="calculated_fee" readonly>
                                    <div class="form-text">
                                        计算规则：(基础费1元/㎡ + 电梯费 + 车位费 + 设施费) × 12个月<br>
                                        <small class="text-muted">注：地下室不计入物业费用</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 表单错误 -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'commercial_properties:list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const areaInput = document.getElementById('id_area');
        const floorInput = document.getElementById('id_floor');
        const hasBasementInput = document.getElementById('id_has_basement');
        const basementNumberField = document.getElementById('basement_number_field');
        const hasParkingInput = document.getElementById('id_has_parking');
        const parkingNumberField = document.getElementById('parking_number_field');
        const calculatedFeeInput = document.getElementById('calculated_fee');
        
        // 地下室复选框变化时显示/隐藏地下室号字段
        if (hasBasementInput && basementNumberField) {
            hasBasementInput.addEventListener('change', function() {
                if (this.checked) {
                    basementNumberField.style.display = 'block';
                } else {
                    basementNumberField.style.display = 'none';
                    document.getElementById('id_basement_number').value = '';
                }
                calculatePropertyFee();
            });
            
            // 页面加载时检查初始状态
            if (hasBasementInput.checked) {
                basementNumberField.style.display = 'block';
            }
        }
        
        // 车位复选框变化时显示/隐藏车位号字段
        if (hasParkingInput && parkingNumberField) {
            hasParkingInput.addEventListener('change', function() {
                if (this.checked) {
                    parkingNumberField.style.display = 'block';
                } else {
                    parkingNumberField.style.display = 'none';
                    document.getElementById('id_parking_number').value = '';
                }
                calculatePropertyFee();
            });
            
            // 页面加载时检查初始状态
            if (hasParkingInput.checked) {
                parkingNumberField.style.display = 'block';
            }
        }
        
        // 计算物业费
        function calculatePropertyFee() {
            const area = parseFloat(areaInput.value) || 0;
            const floor = parseInt(floorInput.value) || 1;
            const hasBasement = hasBasementInput.checked;
            const hasParking = hasParkingInput.checked;
            
            if (area > 0) {
                // 基础费用：每平米1元
                const baseFee = area * 1.0;
                
                // 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
                const elevatorFee = floor <= 11 ? area * 0.3 : area * 0.35;

                // 车位管理费：有车位每月20元
                const parkingFee = hasParking ? 20.0 : 0.0;

                // 共用设施设备运行维护费：每月10元
                const facilityFee = 10.0;

                // 月费用（地下室不计入费用）
                const monthlyFee = baseFee + elevatorFee + parkingFee + facilityFee;
                
                // 年费用
                const yearlyFee = monthlyFee * 12;
                
                calculatedFeeInput.value = '¥' + yearlyFee.toFixed(2);
            }
        }
        
        // 监听输入变化
        if (areaInput) {
            areaInput.addEventListener('input', calculatePropertyFee);
        }
        
        if (floorInput) {
            floorInput.addEventListener('input', calculatePropertyFee);
        }
        
        // 页面加载时计算一次
        calculatePropertyFee();
    });
</script>
{% endblock %}
