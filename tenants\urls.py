from django.urls import path
from . import views

app_name = 'tenants'

urlpatterns = [
    path('', views.TenantListView.as_view(), name='list'),
    path('add/', views.TenantCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', views.TenantUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.TenantDeleteView.as_view(), name='delete'),
    path('overdue/', views.OverdueTenantListView.as_view(), name='overdue'),
    path('checkout/', views.CheckoutTenantListView.as_view(), name='checkout'),
    path('payment-history/', views.PaymentHistoryListView.as_view(), name='payment_history'),
    path('<int:pk>/renew/', views.RenewTenantView.as_view(), name='renew'),
    path('<int:pk>/checkout-action/', views.CheckoutTenantView.as_view(), name='checkout_action'),
    path('export/', views.ExportTenantView.as_view(), name='export'),
    path('import/', views.ImportTenantView.as_view(), name='import'),
    path('download-template/', views.DownloadTemplateView.as_view(), name='download_template'),
    path('<int:pk>/restore/', views.RestoreTenantView.as_view(), name='restore'),
    path('batch-delete-checkout/', views.BatchDeleteCheckoutView.as_view(), name='batch_delete_checkout'),
    path('batch-delete-payment/', views.BatchDeletePaymentView.as_view(), name='batch_delete_payment'),
    path('export-payment-history/', views.ExportPaymentHistoryView.as_view(), name='export_payment_history'),
]
