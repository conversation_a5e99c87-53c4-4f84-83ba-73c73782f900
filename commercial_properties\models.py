from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal


class CommercialProperty(models.Model):
    """商品房模型"""
    # 基本信息
    building_number = models.CharField('楼号', max_length=10)
    unit_number = models.CharField('单元号', max_length=10, blank=True)
    room_number = models.CharField('房号', max_length=10)
    area = models.DecimalField('平米数', max_digits=8, decimal_places=2)
    owner_name = models.CharField('业主姓名', max_length=50)
    owner_phone = models.CharField('业主电话', max_length=20)
    floor = models.IntegerField('楼层', default=1)

    # 附加设施
    has_basement = models.BooleanField('地下室', default=False)
    basement_number = models.CharField('地下室号', max_length=20, blank=True, null=True)
    has_parking = models.BooleanField('车位', default=False)
    parking_number = models.CharField('车位号', max_length=20, blank=True, null=True)

    # 费用信息
    property_fee_due_date = models.DateField('物业费到期日期')
    property_fee_total = models.DecimalField('物业费总额', max_digits=10, decimal_places=2)

    # 状态
    STATUS_CHOICES = [
        ('active', '正常'),
        ('overdue', '逾期'),
    ]
    status = models.CharField('状态', max_length=10, choices=STATUS_CHOICES, default='active')

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '商品房'
        verbose_name_plural = '商品房'
        ordering = ['-created_at']

    def __str__(self):
        unit_str = f"-{self.unit_number}" if self.unit_number else ""
        return f"{self.building_number}{unit_str}-{self.room_number} {self.owner_name}"

    @property
    def days_until_due(self):
        """距离到期天数"""
        if self.property_fee_due_date:
            delta = self.property_fee_due_date - timezone.now().date()
            return delta.days
        return 0

    @property
    def is_overdue(self):
        """是否逾期"""
        return self.days_until_due < 0

    @property
    def overdue_days(self):
        """逾期天数"""
        if self.is_overdue:
            return abs(self.days_until_due)
        return 0

    def calculate_monthly_property_fee(self):
        """计算月物业费"""
        if not self.area or not self.floor:
            return Decimal('0.00')

        # 基础费用：每平米1元
        base_fee = self.area * Decimal('1.00')

        # 电梯费：11层以下(含11层)每平米0.3元，11层以上每平米0.35元
        if self.floor <= 11:
            elevator_fee = self.area * Decimal('0.30')
        else:
            elevator_fee = self.area * Decimal('0.35')

        # 地下室管理费：有地下室每月10元
        basement_fee = Decimal('10.00') if self.has_basement else Decimal('0.00')

        # 车位管理费：有车位每月20元
        parking_fee = Decimal('20.00') if self.has_parking else Decimal('0.00')

        # 共用设施设备运行维护费：每月10元
        facility_fee = Decimal('10.00')

        monthly_fee = base_fee + elevator_fee + basement_fee + parking_fee + facility_fee
        return monthly_fee.quantize(Decimal('0.01'))

    def calculate_property_fee(self):
        """计算年物业费总额"""
        monthly_fee = self.calculate_monthly_property_fee()
        return (monthly_fee * 12).quantize(Decimal('0.01'))

    def save(self, *args, **kwargs):
        # 自动计算物业费
        if self.area and self.floor:
            self.property_fee_total = self.calculate_property_fee()

        # 更新状态
        if self.property_fee_due_date:
            if self.is_overdue and self.status == 'active':
                self.status = 'overdue'
            elif not self.is_overdue and self.status == 'overdue':
                self.status = 'active'

        super().save(*args, **kwargs)


class CommercialPropertyPaymentHistory(models.Model):
    """商品房缴费流水"""
    property = models.ForeignKey(CommercialProperty, on_delete=models.CASCADE, verbose_name='商品房')
    payment_date = models.DateTimeField('交费时间', default=timezone.now)
    amount = models.DecimalField('缴费金额', max_digits=10, decimal_places=2)
    fee_start_date = models.DateField('物业费开始时间')
    fee_end_date = models.DateField('物业费结束时间')
    payment_method = models.CharField('缴费方式', max_length=20, default='现金')
    notes = models.TextField('备注', blank=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '商品房缴费记录'
        verbose_name_plural = '商品房缴费记录'
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.property} - ¥{self.amount} - {self.payment_date.strftime('%Y-%m-%d')}"
