{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客退房列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-sign-out-alt me-2 text-warning"></i>租客退房列表
                        <span class="badge bg-warning ms-2">{{ tenants|length }}</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'tenants:export' %}?status=checkout" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>导出退房数据
                                </a>
                                <button type="button" class="btn btn-danger" id="batch-delete" disabled>
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                    <th>楼号</th>
                                    <th>房号</th>
                                    <th>平米数</th>
                                    <th>租客姓名</th>
                                    <th>身份证号</th>
                                    <th>房东姓名</th>
                                    <th>房东电话</th>
                                    <th>入住时间</th>
                                    <th>退房日期</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tenant in tenants %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input item-checkbox" value="{{ tenant.pk }}">
                                    </td>
                                    <td>{{ tenant.building_number }}</td>
                                    <td>{{ tenant.room_number }}</td>
                                    <td>{{ tenant.area }}㎡</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator" style="background-color: #6c757d;"></div>
                                            {{ tenant.tenant_name }}
                                        </div>
                                    </td>
                                    <td>{{ tenant.id_card }}</td>
                                    <td>{{ tenant.landlord_name }}</td>
                                    <td>{{ tenant.landlord_phone }}</td>
                                    <td>{{ tenant.move_in_date }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ tenant.updated_at|date:"Y-m-d" }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-success" title="恢复租客" 
                                                    onclick="restoreTenant({{ tenant.pk }})">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <a href="{% url 'tenants:delete' tenant.pk %}" class="btn btn-outline-danger btn-delete" 
                                               title="彻底删除" data-message="确定要彻底删除租客 {{ tenant.tenant_name }} 吗？删除后无法恢复！">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">太好了！目前没有退房的租客</p>
                                        <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>查看所有租客
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 统计信息 -->
                    {% if tenants %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-secondary">
                                <h6 class="alert-heading">
                                    <i class="fas fa-chart-bar me-2"></i>退房统计
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>总退房数：</strong>{{ tenants|length }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>本月退房：</strong>
                                        {% with this_month_count=0 %}
                                            {% now "Y-m" as current_month %}
                                            {% for tenant in tenants %}
                                                {% if tenant.updated_at|date:"Y-m" == current_month %}
                                                    {% with this_month_count=this_month_count|add:1 %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                            {{ this_month_count }}户
                                        {% endwith %}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>最近退房：</strong>
                                        {% if tenants %}
                                            {{ tenants.0.updated_at|date:"Y-m-d" }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>状态：</strong>
                                        <span class="badge bg-secondary">已退房</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除确认对话框
        var deleteButtons = document.querySelectorAll('.btn-delete');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                var message = this.getAttribute('data-message') || '确定要删除这条记录吗？';
                if (confirm(message)) {
                    window.location.href = this.href;
                }
            });
        });
        
        // 全选功能
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const batchDeleteBtn = document.getElementById('batch-delete');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                updateBatchButtons();
            });
        }
        
        itemCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                updateBatchButtons();
                updateSelectAllCheckbox();
            });
        });
        
        function updateBatchButtons() {
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
        }
        
        function updateSelectAllCheckbox() {
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            
            if (checkedBoxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedBoxes.length === itemCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }
        
        // 批量删除
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要删除的租客');
                    return;
                }
                
                if (confirm(`确定要彻底删除选中的 ${checkedBoxes.length} 个退房租客吗？删除后无法恢复！`)) {
                    // 实现批量删除逻辑
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{% url "tenants:batch_delete_checkout" %}';
                    
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);
                    
                    checkedBoxes.forEach(function(checkbox) {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'selected_items';
                        input.value = checkbox.value;
                        form.appendChild(input);
                    });
                    
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }
    });
    
    // 恢复租客功能
    function restoreTenant(tenantId) {
        if (confirm('确定要恢复此租客吗？恢复后租客将重新显示在租客列表中。')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/tenants/' + tenantId + '/restore/';
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
