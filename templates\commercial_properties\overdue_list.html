{% extends 'base/base.html' %}
{% load static %}

{% block title %}物业费到期列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        物业费到期列表
                        {% if properties %}
                            <span class="badge bg-warning ms-2">{{ properties|length }}户</span>
                        {% endif %}
                    </h4>
                    
                    <div class="d-flex gap-2">
                        <a href="{% url 'commercial_properties:list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-1"></i>返回列表
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="搜索业主姓名、房号、电话..." 
                                           value="{{ request.GET.search }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <a href="{% url 'commercial_properties:overdue' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-refresh me-1"></i>重置
                                </a>
                            </div>
                        </div>
                    </form>
                    
                    {% if properties %}
                        <!-- 数据表格 -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>楼号</th>
                                        <th>单元号</th>
                                        <th>房号</th>
                                        <th>平米数</th>
                                        <th>业主姓名</th>
                                        <th>地下室</th>
                                        <th>车位</th>
                                        <th>业主电话</th>
                                        <th>年物业费</th>
                                        <th>到期日期</th>
                                        <th>逾期天数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for property in properties %}
                                    <tr class="{% if property.overdue_days > 30 %}table-danger{% elif property.overdue_days > 7 %}table-warning{% endif %}">
                                        <td>{{ property.building_number }}</td>
                                        <td>{{ property.unit_number|default:"-" }}</td>
                                        <td>{{ property.room_number }}</td>
                                        <td>{{ property.area }}㎡</td>
                                        <td>{{ property.owner_name }}</td>
                                        <td>
                                            {% if property.has_basement %}
                                                <span class="badge bg-info">{{ property.basement_number|default:"有" }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">无</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if property.has_parking %}
                                                <span class="badge bg-success">{{ property.parking_number|default:"有" }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">无</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ property.owner_phone }}</td>
                                        <td class="text-success fw-bold">¥{{ property.calculate_property_fee }}</td>
                                        <td>{{ property.property_fee_due_date }}</td>
                                        <td>
                                            <span class="badge bg-danger">{{ property.overdue_days }}天</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{% url 'commercial_properties:renew' property.pk %}" 
                                                   class="btn btn-sm btn-success" title="续费">
                                                    <i class="fas fa-credit-card"></i>
                                                </a>
                                                <a href="{% url 'commercial_properties:edit' property.pk %}" 
                                                   class="btn btn-sm btn-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        {% if is_paginated %}
                            <nav aria-label="分页导航">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                        </li>
                                    {% endif %}
                                    
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
                                    </li>
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                        
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            <h4 class="mt-3 text-muted">太好了！没有逾期的商品房</h4>
                            <p class="text-muted">所有商品房的物业费都是正常状态</p>
                            <a href="{% url 'commercial_properties:list' %}" class="btn btn-primary">
                                <i class="fas fa-list me-1"></i>查看所有商品房
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table-warning {
        background-color: #fff3cd !important;
    }
    .table-danger {
        background-color: #f8d7da !important;
    }
</style>
{% endblock %}
