from django.shortcuts import render
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse

# 临时占位符视图
class CommercialPropertyListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("商品房列表")

class CommercialPropertyCreateView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("商品房创建")

class CommercialPropertyUpdateView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商品房编辑")

class CommercialPropertyDeleteView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商品房删除")

class OverdueCommercialPropertyListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("逾期商品房列表")

class PaymentHistoryListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("商品房缴费流水")

class RenewCommercialPropertyView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("商品房续费")

class ExportCommercialPropertyView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("导出商品房数据")

class ImportCommercialPropertyView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("导入商品房数据")
