from django.shortcuts import render
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse

# 临时占位符视图
class ParkingListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("车位列表")

class ParkingCreateView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("车位创建")

class ParkingUpdateView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("车位编辑")

class ParkingDeleteView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("车位删除")

class OverdueParkingListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("逾期车位列表")

class CheckoutParkingListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("退车位列表")

class PaymentHistoryListView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("车位缴费流水")

class RenewParkingView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("车位续费")

class CheckoutParkingView(LoginRequiredMixin, View):
    def get(self, request, pk):
        return HttpResponse("退车位")

class ExportParkingView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("导出车位数据")

class ImportParkingView(LoginRequiredMixin, View):
    def get(self, request):
        return HttpResponse("导入车位数据")
