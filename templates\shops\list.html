{% extends 'base/base.html' %}
{% load static %}

{% block title %}商铺租房管理 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-store me-2"></i>商铺租房管理
                        <span class="badge bg-success ms-2">正常 {{ total_count }}家</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'shops:add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>新增商铺
                                </a>
                                <a href="{% url 'shops:import' %}" class="btn btn-success">
                                    <i class="fas fa-upload me-1"></i>批量导入
                                </a>
                                <a href="{% url 'shops:export' %}" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>批量导出
                                </a>
                                <button type="button" class="btn btn-danger batch-action" id="batch-delete" disabled>
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                            
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="搜索商铺号、租户姓名、电话..." 
                                           value="{{ request.GET.search }}">
                                    <button type="submit" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商铺列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                    <th>商铺号</th>
                                    <th>门牌号</th>
                                    <th>租户姓名</th>
                                    <th>电话</th>
                                    <th>平米数</th>
                                    <th>月物业费</th>
                                    <th>起租时间</th>
                                    <th>物业费到期时间</th>
                                    <th>剩余天数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shop in shops %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input item-checkbox" value="{{ shop.pk }}">
                                    </td>
                                    <td class="fw-bold">{{ shop.shop_number }}</td>
                                    <td>{{ shop.door_number|default:"-" }}</td>
                                    <td>{{ shop.tenant_name }}</td>
                                    <td>{{ shop.phone }}</td>
                                    <td>{{ shop.area }}㎡</td>
                                    <td class="text-success fw-bold">¥{{ shop.calculate_property_fee }}</td>
                                    <td>{{ shop.lease_start_date }}</td>
                                    <td>{{ shop.property_fee_due_date }}</td>
                                    <td>
                                        {% if shop.days_until_due <= 7 %}
                                            <span class="badge bg-warning">{{ shop.days_until_due }}天</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ shop.days_until_due }}天</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'shops:edit' shop.pk %}" class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'shops:renew' shop.pk %}" class="btn btn-outline-success" title="续费">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-warning" title="退房" 
                                                    onclick="checkoutShop({{ shop.pk }}, '{{ shop.shop_number }}')">
                                                <i class="fas fa-sign-out-alt"></i>
                                            </button>
                                            <a href="{% url 'shops:delete' shop.pk %}" class="btn btn-outline-danger" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">暂无正常状态的商铺</p>
                                        <div>
                                            <a href="{% url 'shops:add' %}" class="btn btn-primary me-2">
                                                <i class="fas fa-plus me-1"></i>添加商铺
                                            </a>
                                            <a href="{% url 'shops:overdue' %}" class="btn btn-outline-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>查看到期列表
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 费用统计 -->
                    {% if shops %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-chart-bar me-2"></i>费用统计
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>总商铺数：</strong>{{ shops|length }}家
                                    </div>
                                    <div class="col-md-3">
                                        <strong>总面积：</strong>
                                        <span id="total-area">计算中...</span>㎡
                                    </div>
                                    <div class="col-md-3">
                                        <strong>月收入总额：</strong>
                                        ¥<span id="monthly-income">计算中...</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>年收入总额：</strong>
                                        ¥<span id="yearly-income">计算中...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    // 商铺退房功能
    function checkoutShop(shopId, shopNumber) {
        if (confirm(`确定要将商铺 ${shopNumber} 设为退房状态吗？`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/shops/${shopId}/checkout-action/`;
            form.style.display = 'none';
            
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    // 批量操作功能
    document.addEventListener('DOMContentLoaded', function() {
        // 全选/取消全选
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const batchDeleteBtn = document.getElementById('batch-delete');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBatchButtons();
            });
        }

        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchButtons);
        });

        function updateBatchButtons() {
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            if (batchDeleteBtn) {
                batchDeleteBtn.disabled = checkedCount === 0;
            }
        }

        // 计算统计数据
        calculateStats();

        function calculateStats() {
            let totalArea = 0;
            let monthlyIncome = 0;

            // 从表格中获取数据
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const areaCells = row.querySelectorAll('td');
                if (areaCells.length > 5) {
                    // 获取平米数（第6列，索引5）
                    const areaText = areaCells[5].textContent.replace('㎡', '');
                    const area = parseFloat(areaText) || 0;
                    totalArea += area;

                    // 获取月物业费（第7列，索引6）
                    const feeText = areaCells[6].textContent.replace('¥', '');
                    const fee = parseFloat(feeText) || 0;
                    monthlyIncome += fee;
                }
            });

            // 更新显示
            const totalAreaSpan = document.getElementById('total-area');
            const monthlyIncomeSpan = document.getElementById('monthly-income');
            const yearlyIncomeSpan = document.getElementById('yearly-income');

            if (totalAreaSpan) {
                totalAreaSpan.textContent = totalArea.toFixed(2);
            }
            if (monthlyIncomeSpan) {
                monthlyIncomeSpan.textContent = monthlyIncome.toFixed(2);
            }
            if (yearlyIncomeSpan) {
                yearlyIncomeSpan.textContent = (monthlyIncome * 12).toFixed(2);
            }
        }
    });
</script>
{% endblock %}
