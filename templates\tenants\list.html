{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>租客管理
                        <span class="badge bg-primary ms-2">{{ total_count }}</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'tenants:add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>新增租客
                                </a>
                                <a href="{% url 'tenants:export' %}" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>批量导出
                                </a>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 筛选条件 -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <form method="get" class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">楼号</label>
                                        {{ search_form.building_number }}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">状态</label>
                                        {{ search_form.status }}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">筛选</button>
                                            <a href="{% url 'tenants:list' %}" class="btn btn-outline-secondary">重置</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>楼号</th>
                                    <th>房号</th>
                                    <th>平米数</th>
                                    <th>租客姓名</th>
                                    <th>身份证号</th>
                                    <th>租住人数</th>
                                    <th>房东姓名</th>
                                    <th>房东电话</th>
                                    <th>入住时间</th>
                                    <th>物业费到期时间</th>
                                    <th>剩余天数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tenant in tenants %}
                                <tr>
                                    <td>{{ tenant.building_number }}</td>
                                    <td>{{ tenant.room_number }}</td>
                                    <td>{{ tenant.area }}㎡</td>
                                    <td>{{ tenant.tenant_name }}</td>
                                    <td>{{ tenant.id_card }}</td>
                                    <td>{{ tenant.resident_count }}人</td>
                                    <td>{{ tenant.landlord_name }}</td>
                                    <td>{{ tenant.landlord_phone }}</td>
                                    <td>{{ tenant.move_in_date }}</td>
                                    <td>{{ tenant.property_fee_due_date }}</td>
                                    <td>
                                        {% if tenant.days_until_due <= 7 %}
                                            <span class="badge bg-warning">{{ tenant.days_until_due }}天</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ tenant.days_until_due }}天</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'tenants:edit' tenant.pk %}" class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无租客数据</p>
                                        <a href="{% url 'tenants:add' %}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>添加第一个租客
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('租客列表页面加载完成');
    });
</script>
{% endblock %}
