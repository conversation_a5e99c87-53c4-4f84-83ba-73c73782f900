{% extends 'base/base.html' %}
{% load static %}

{% block title %}商品房续费 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>商品房续费
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 商品房信息 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-home me-1"></i>商品房信息
                            </h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>楼号房号：</strong></td>
                                    <td>{{ property.building_number }}{% if property.unit_number %}-{{ property.unit_number }}{% endif %}-{{ property.room_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>业主姓名：</strong></td>
                                    <td>{{ property.owner_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>业主电话：</strong></td>
                                    <td>{{ property.owner_phone }}</td>
                                </tr>
                                <tr>
                                    <td><strong>平米数：</strong></td>
                                    <td>{{ property.area }}㎡</td>
                                </tr>
                                <tr>
                                    <td><strong>楼层：</strong></td>
                                    <td>{{ property.floor }}层</td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-calculator me-1"></i>费用信息
                            </h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>地下室：</strong></td>
                                    <td>
                                        {% if property.has_basement %}
                                            <span class="badge bg-info">{{ property.basement_number|default:"有" }}</span>
                                            <small class="text-muted">(+¥10/月)</small>
                                        {% else %}
                                            <span class="badge bg-secondary">无</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>车位：</strong></td>
                                    <td>
                                        {% if property.has_parking %}
                                            <span class="badge bg-success">{{ property.parking_number|default:"有" }}</span>
                                            <small class="text-muted">(+¥20/月)</small>
                                        {% else %}
                                            <span class="badge bg-secondary">无</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>月物业费：</strong></td>
                                    <td class="text-success fw-bold">¥{{ property.calculate_monthly_property_fee }}</td>
                                </tr>
                                <tr>
                                    <td><strong>当前到期日：</strong></td>
                                    <td>{{ property.property_fee_due_date }}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态：</strong></td>
                                    <td>
                                        {% if property.status == 'overdue' %}
                                            <span class="badge bg-danger">逾期{{ property.overdue_days }}天</span>
                                        {% else %}
                                            <span class="badge bg-success">正常</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 续费表单 -->
                    <form method="post" class="border-top pt-4">
                        {% csrf_token %}
                        
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-credit-card me-1"></i>续费信息
                        </h6>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.months.id_for_label }}" class="form-label">续费月数 *</label>
                                    {{ form.months }}
                                    {% if form.months.errors %}
                                        <div class="text-danger">{{ form.months.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.amount.id_for_label }}" class="form-label">续费金额</label>
                                    {{ form.amount }}
                                    {% if form.amount.errors %}
                                        <div class="text-danger">{{ form.amount.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.payment_method.id_for_label }}" class="form-label">缴费方式 *</label>
                                    {{ form.payment_method }}
                                    {% if form.payment_method.errors %}
                                        <div class="text-danger">{{ form.payment_method.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">备注</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors.0 }}</div>
                            {% endif %}
                        </div>
                        
                        <!-- 续费后信息预览 -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-1"></i>续费后信息预览</h6>
                            <p class="mb-1"><strong>新到期日期：</strong><span id="new_due_date">-</span></p>
                            <p class="mb-0"><strong>续费金额：</strong><span id="total_amount" class="text-success fw-bold">-</span></p>
                        </div>
                        
                        <!-- 表单错误 -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'commercial_properties:overdue' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-credit-card me-1"></i>确认续费
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const monthsInput = document.getElementById('id_months');
        const amountInput = document.getElementById('id_amount');
        const newDueDateSpan = document.getElementById('new_due_date');
        const totalAmountSpan = document.getElementById('total_amount');
        
        const monthlyFee = {{ property.calculate_monthly_property_fee }};
        const currentDueDate = new Date('{{ property.property_fee_due_date|date:"Y-m-d" }}');
        
        function updatePreview() {
            const months = parseInt(monthsInput.value) || 0;
            
            if (months > 0) {
                // 计算续费金额
                const totalAmount = monthlyFee * months;
                amountInput.value = totalAmount.toFixed(2);
                totalAmountSpan.textContent = '¥' + totalAmount.toFixed(2);
                
                // 计算新到期日期
                const newDueDate = new Date(currentDueDate);
                newDueDate.setMonth(newDueDate.getMonth() + months);
                
                const year = newDueDate.getFullYear();
                const month = String(newDueDate.getMonth() + 1).padStart(2, '0');
                const day = String(newDueDate.getDate()).padStart(2, '0');
                
                newDueDateSpan.textContent = `${year}-${month}-${day}`;
            } else {
                amountInput.value = '0.00';
                totalAmountSpan.textContent = '-';
                newDueDateSpan.textContent = '-';
            }
        }
        
        // 监听月数变化
        if (monthsInput) {
            monthsInput.addEventListener('input', updatePreview);
            // 页面加载时更新一次
            updatePreview();
        }
    });
</script>
{% endblock %}
