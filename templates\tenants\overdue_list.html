{% extends 'base/base.html' %}
{% load static %}

{% block title %}物业费到期列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2 text-warning"></i>物业费到期列表
                        <span class="badge bg-danger ms-2">{{ tenants|length }}</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'tenants:export' %}?status=overdue" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>导出逾期数据
                                </a>
                                <button type="button" class="btn btn-danger batch-action" id="batch-delete" disabled>
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'tenants:overdue' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                    <th>楼号</th>
                                    <th>房号</th>
                                    <th>平米数</th>
                                    <th>租客姓名</th>
                                    <th>联系电话</th>
                                    <th>物业费到期时间</th>
                                    <th>已逾期天数</th>
                                    <th>月物业费</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tenant in tenants %}
                                <tr class="{% if tenant.overdue_days > 30 %}table-danger{% elif tenant.overdue_days > 7 %}table-warning{% endif %}">
                                    <td>
                                        <input type="checkbox" class="form-check-input item-checkbox" value="{{ tenant.pk }}">
                                    </td>
                                    <td>{{ tenant.building_number }}</td>
                                    <td>{{ tenant.room_number }}</td>
                                    <td>{{ tenant.area }}㎡</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator status-overdue"></div>
                                            {{ tenant.tenant_name }}
                                        </div>
                                    </td>
                                    <td>{{ tenant.landlord_phone }}</td>
                                    <td>{{ tenant.property_fee_due_date }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ tenant.overdue_days }}天</span>
                                    </td>
                                    <td class="text-success fw-bold">¥{{ tenant.property_fee }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'tenants:renew' tenant.pk %}" class="btn btn-success" title="续费">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                            <form method="post" action="{% url 'tenants:checkout_action' tenant.pk %}" style="display: inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-warning" title="退房" 
                                                        onclick="return confirm('确定要将此租客标记为退房吗？')">
                                                    <i class="fas fa-sign-out-alt"></i>
                                                </button>
                                            </form>
                                            <a href="{% url 'tenants:delete' tenant.pk %}" class="btn btn-danger btn-delete" 
                                               title="删除" data-message="确定要删除租客 {{ tenant.tenant_name }} 吗？">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">太好了！目前没有逾期的租客</p>
                                        <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>查看所有租客
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 统计信息 -->
                    {% if tenants %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-chart-bar me-2"></i>逾期统计
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>总逾期数：</strong>{{ tenants|length }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>逾期7天内：</strong>{{ short_overdue }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>逾期30天以上：</strong>{{ long_overdue }}户
                                    </div>
                                    <div class="col-md-3">
                                        <strong>应收金额：</strong>¥{{ total_overdue_amount|floatformat:2 }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 批量删除功能
        const batchDeleteBtn = document.getElementById('batch-delete');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要删除的租客');
                    return;
                }
                
                if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个逾期租客吗？`)) {
                    // 实现批量删除逻辑
                    console.log('批量删除逾期租客');
                }
            });
        }
        
        // 高亮显示严重逾期的行
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach(function(row) {
            const overdueCell = row.querySelector('.badge');
            if (overdueCell) {
                const days = parseInt(overdueCell.textContent);
                if (days > 30) {
                    row.classList.add('table-danger');
                } else if (days > 7) {
                    row.classList.add('table-warning');
                }
            }
        });
    });
</script>
{% endblock %}
