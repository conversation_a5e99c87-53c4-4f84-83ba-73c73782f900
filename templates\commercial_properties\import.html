{% extends 'base/base.html' %}
{% load static %}

{% block title %}批量导入商品房 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-upload me-2"></i>批量导入商品房
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 导入说明 -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>导入说明
                        </h6>
                        <ul class="mb-0">
                            <li>请使用Excel文件(.xlsx或.xls格式)</li>
                            <li>第一行为表头，从第二行开始为数据</li>
                            <li>必填字段：楼号、房号、平米数、业主姓名、业主电话</li>
                            <li>可选字段：单元号、地下室、车位、物业费到期日期、楼层</li>
                        </ul>
                    </div>
                    
                    <!-- Excel模板下载 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-download fa-3x text-primary mb-3"></i>
                                    <h6>下载Excel模板</h6>
                                    <p class="text-muted small">下载标准的Excel导入模板</p>
                                    <a href="{% url 'commercial_properties:download_template' %}" class="btn btn-primary">
                                        <i class="fas fa-download me-1"></i>下载模板
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-table me-2"></i>Excel表格格式</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr class="table-primary">
                                                <th>楼号*</th>
                                                <th>单元号</th>
                                                <th>房号*</th>
                                                <th>平米数*</th>
                                                <th>业主姓名*</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>A1</td>
                                                <td>1</td>
                                                <td>101</td>
                                                <td>120.5</td>
                                                <td>张三</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <small class="text-muted">* 表示必填字段</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 导入表单 -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.file.id_for_label }}" class="form-label">
                                <i class="fas fa-file-excel me-1"></i>选择Excel文件 *
                            </label>
                            {{ form.file }}
                            {% if form.file.errors %}
                                <div class="text-danger">{{ form.file.errors.0 }}</div>
                            {% endif %}
                            <div class="form-text">
                                支持.xlsx和.xls格式，文件大小不超过10MB
                            </div>
                        </div>
                        
                        <!-- 导入选项 -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" checked>
                                <label class="form-check-label" for="skip_duplicates">
                                    跳过重复的楼号房号组合
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing">
                                <label class="form-check-label" for="update_existing">
                                    更新已存在的商品房信息
                                </label>
                            </div>
                        </div>
                        
                        <!-- 表单错误 -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}
                        
                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'commercial_properties:list' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i>取消
                            </a>
                            <button type="submit" class="btn btn-success" id="import-btn">
                                <i class="fas fa-upload me-1"></i>开始导入
                            </button>
                        </div>
                    </form>
                    
                    <!-- 导入进度 -->
                    <div id="import-progress" class="mt-4" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted">正在导入数据，请稍候...</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const importBtn = document.getElementById('import-btn');
        const progressDiv = document.getElementById('import-progress');
        const progressBar = progressDiv.querySelector('.progress-bar');
        
        form.addEventListener('submit', function(e) {
            const fileInput = document.getElementById('id_file');
            
            if (!fileInput.files.length) {
                e.preventDefault();
                alert('请选择要导入的Excel文件');
                return;
            }
            
            // 显示进度条
            importBtn.disabled = true;
            importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导入中...';
            progressDiv.style.display = 'block';
            
            // 模拟进度
            let progress = 0;
            const interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);
            
            // 表单提交后清理
            setTimeout(function() {
                clearInterval(interval);
                progressBar.style.width = '100%';
            }, 1000);
        });
        
        // 文件选择验证
        const fileInput = document.getElementById('id_file');
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const fileName = file.name.toLowerCase();
                    const validExtensions = ['.xlsx', '.xls'];
                    const isValid = validExtensions.some(ext => fileName.endsWith(ext));
                    
                    if (!isValid) {
                        alert('请选择Excel文件(.xlsx或.xls格式)');
                        this.value = '';
                        return;
                    }
                    
                    if (file.size > 10 * 1024 * 1024) { // 10MB
                        alert('文件大小不能超过10MB');
                        this.value = '';
                        return;
                    }
                }
            });
        }
    });
</script>
{% endblock %}
