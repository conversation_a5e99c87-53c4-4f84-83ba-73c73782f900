from django import forms
from django.utils import timezone
from datetime import datetime, timedelta
from .models import Tenant, TenantPaymentHistory


class TenantForm(forms.ModelForm):
    """租客表单"""
    
    class Meta:
        model = Tenant
        fields = [
            'building_number', 'room_number', 'area', 'floor',
            'tenant_name', 'id_card', 'resident_count',
            'landlord_name', 'landlord_phone',
            'move_in_date', 'property_fee_due_date'
        ]
        widgets = {
            'building_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入楼号'
            }),
            'room_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入房号',
                'id': 'id_room_number'
            }),
            'area': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '平米数',
                'id': 'id_area',
                'readonly': True
            }),
            'floor': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '楼层',
                'id': 'id_floor'
            }),
            'tenant_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入租客姓名'
            }),
            'id_card': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入身份证号'
            }),
            'resident_count': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '租住人数',
                'min': 1
            }),
            'landlord_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入房东姓名'
            }),
            'landlord_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入房东电话'
            }),
            'move_in_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': True
            }),
            'property_fee_due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'required': True
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 设置默认日期
        if not self.instance.pk:
            # 新增时设置默认值
            self.fields['move_in_date'].initial = timezone.now().date()
            self.fields['property_fee_due_date'].initial = timezone.now().date() + timedelta(days=365)
        else:
            # 编辑时手动设置初始值和widget的value
            if self.instance.move_in_date:
                self.fields['move_in_date'].initial = self.instance.move_in_date
                self.fields['move_in_date'].widget.attrs['value'] = self.instance.move_in_date.strftime('%Y-%m-%d')
            if self.instance.property_fee_due_date:
                self.fields['property_fee_due_date'].initial = self.instance.property_fee_due_date
                self.fields['property_fee_due_date'].widget.attrs['value'] = self.instance.property_fee_due_date.strftime('%Y-%m-%d')

    def clean(self):
        cleaned_data = super().clean()
        building_number = cleaned_data.get('building_number')
        room_number = cleaned_data.get('room_number')

        if building_number and room_number:
            # 检查是否存在相同楼号房号的活跃租客
            existing_query = Tenant.objects.filter(
                building_number=building_number,
                room_number=room_number,
                status__in=['active', 'overdue']  # 只检查活跃和逾期的租客
            )

            # 如果是编辑，排除当前实例
            if self.instance.pk:
                existing_query = existing_query.exclude(pk=self.instance.pk)

                # 编辑时，如果楼号房号没有改变，不需要验证
                if (self.instance.building_number == building_number and
                    self.instance.room_number == room_number):
                    return cleaned_data

            if existing_query.exists():
                existing_tenant = existing_query.first()
                self.add_error('building_number', f'楼号 {building_number} 房号 {room_number} 已有租客 {existing_tenant.tenant_name} 入住')
                self.add_error('room_number', '只有在租客退房后才能重新入住')

        return cleaned_data


class TenantSearchForm(forms.Form):
    """租客搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索租客姓名、房号、身份证号...',
            'name': 'search'
        })
    )


class TenantRenewForm(forms.Form):
    """租客续费表单"""
    months = forms.IntegerField(
        label='续费月数',
        min_value=1,
        max_value=24,
        initial=12,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入续费月数'
        })
    )
    payment_method = forms.CharField(
        label='缴费方式',
        initial='现金',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '缴费方式'
        })
    )
    notes = forms.CharField(
        label='备注',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '备注信息'
        })
    )

    def __init__(self, tenant=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.tenant = tenant


class PaymentHistorySearchForm(forms.Form):
    """缴费流水搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索租客姓名、房号...'
        })
    )
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class TenantImportForm(forms.Form):
    """租客导入表单"""
    file = forms.FileField(
        label='Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls'
        })
    )
    
    def clean_file(self):
        file = self.cleaned_data['file']
        if file:
            if not file.name.endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('请上传Excel文件(.xlsx或.xls格式)')
        return file
