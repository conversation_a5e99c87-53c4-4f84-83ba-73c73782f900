from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

from .models import Shop, ShopPaymentHistory
from .forms import ShopForm, ShopSearchForm, ShopPaymentForm, ShopImportForm, ShopPaymentHistorySearchForm


class ShopListView(LoginRequiredMixin, ListView):
    """商铺列表视图"""
    model = Shop
    template_name = 'shops/list.html'
    context_object_name = 'shops'
    paginate_by = 20

    def get_queryset(self):
        # 自动更新逾期状态
        today = timezone.now().date()
        Shop.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态
        Shop.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = Shop.objects.filter(status='active')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search) |
                Q(door_number__icontains=search)
            )

        # 状态筛选
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        return queryset.order_by('shop_number')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ShopSearchForm(self.request.GET)
        context['total_count'] = self.get_queryset().count()
        return context


class ShopCreateView(LoginRequiredMixin, CreateView):
    """商铺创建视图"""
    model = Shop
    form_class = ShopForm
    template_name = 'shops/form.html'
    success_url = reverse_lazy('shops:list')

    def form_valid(self, form):
        messages.success(self.request, '商铺信息添加成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增商铺'
        return context


class ShopUpdateView(LoginRequiredMixin, UpdateView):
    """商铺更新视图"""
    model = Shop
    form_class = ShopForm
    template_name = 'shops/form.html'
    success_url = reverse_lazy('shops:list')

    def form_valid(self, form):
        messages.success(self.request, '商铺信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑商铺'
        return context


class ShopDeleteView(LoginRequiredMixin, DeleteView):
    """商铺删除视图"""
    model = Shop
    template_name = 'shops/shop_confirm_delete.html'
    success_url = reverse_lazy('shops:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '商铺删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueShopListView(LoginRequiredMixin, ListView):
    """逾期商铺列表视图"""
    model = Shop
    template_name = 'shops/overdue_list.html'
    context_object_name = 'shops'
    paginate_by = 20

    def get_queryset(self):
        # 自动更新逾期状态
        today = timezone.now().date()
        Shop.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        queryset = Shop.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search)
            )

        return queryset.order_by('property_fee_due_date')


class ShopRenewView(LoginRequiredMixin, View):
    """商铺续费视图"""
    template_name = 'shops/renew.html'

    def get(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk)
        form = ShopPaymentForm(shop=shop)
        return render(request, self.template_name, {
            'shop': shop,
            'form': form
        })

    def post(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk)
        form = ShopPaymentForm(shop, request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data.get('notes', '')

            # 计算续费金额
            monthly_fee = shop.calculate_property_fee()
            total_amount = monthly_fee * months

            # 计算新的到期日期
            current_due_date = shop.property_fee_due_date

            # 使用dateutil的relativedelta来正确计算月份
            from dateutil.relativedelta import relativedelta

            # 续费开始日期：从当前到期日期的下一天开始
            fee_start_date = current_due_date + timedelta(days=1)

            # 计算新的到期日期
            new_due_date = fee_start_date + relativedelta(months=months) - timedelta(days=1)

            # 创建缴费记录
            ShopPaymentHistory.objects.create(
                shop=shop,
                amount=total_amount,
                fee_start_date=fee_start_date,
                fee_end_date=new_due_date,
                payment_method=payment_method,
                notes=notes
            )

            # 更新商铺到期日期和状态
            shop.property_fee_due_date = new_due_date
            shop.status = 'active'
            shop.save()

            messages.success(request, f'商铺 {shop.shop_number} 续费成功！新到期日期：{new_due_date}')
            return redirect('shops:list')

        return render(request, self.template_name, {
            'shop': shop,
            'form': form
        })


class CheckoutShopListView(LoginRequiredMixin, ListView):
    """退房商铺列表视图"""
    model = Shop
    template_name = 'shops/checkout_list.html'
    context_object_name = 'shops'
    paginate_by = 20

    def get_queryset(self):
        queryset = Shop.objects.filter(status='checkout')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop_number__icontains=search) |
                Q(tenant_name__icontains=search) |
                Q(phone__icontains=search)
            )

        return queryset.order_by('-updated_at')


class CheckoutShopView(LoginRequiredMixin, View):
    """商铺退房视图"""

    def post(self, request, pk):
        shop = get_object_or_404(Shop, pk=pk)

        # 更新状态为已退房
        shop.status = 'checkout'
        shop.save()

        messages.success(request, f'商铺 {shop.shop_number} 已成功退房！')
        return redirect('shops:list')


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """商铺缴费流水视图"""
    model = ShopPaymentHistory
    template_name = 'shops/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = ShopPaymentHistory.objects.select_related('shop')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(shop__shop_number__icontains=search) |
                Q(shop__tenant_name__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = ShopPaymentHistorySearchForm(self.request.GET)

        # 计算统计数据
        queryset = self.get_queryset()

        if queryset.exists():
            # 总统计
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0

            # 本月统计
            now = timezone.now()
            local_now = timezone.localtime(now)

            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month_start = this_month_start.replace(month=local_now.month + 1) if local_now.month < 12 else this_month_start.replace(year=local_now.year + 1, month=1)

            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 上月统计
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context


class GetShopInfoView(LoginRequiredMixin, View):
    """获取商铺信息API"""

    def get(self, request):
        shop_number = request.GET.get('shop_number', '')

        # 根据您提供的数据创建商铺信息映射
        shop_data = {
            '1-101': {'door_number': '顺安路555-1', 'area': 74.85},
            '1-102': {'door_number': '顺安路555-3', 'area': 98.64},
            '1-103': {'door_number': '顺安路555-5', 'area': 98.81},
            '1-104': {'door_number': '顺安路555-7', 'area': 74.85},
            '2-101': {'door_number': '顺安路555-9', 'area': 74.72},
            '2-102': {'door_number': '顺安路555-11', 'area': 98.47},
            '2-103': {'door_number': '顺安路555-13', 'area': 95.29},
            '2-104': {'door_number': '顺安路555-15', 'area': 74.72},
            '2-105': {'door_number': '顺安路555-17', 'area': 65.03},
            '2-106': {'door_number': '顺安路555-19', 'area': 64.83},
            '3-101': {'door_number': '顺安路555-21', 'area': 64.89},
            '3-102': {'door_number': '顺安路555-23', 'area': 65.04},
            '3-103': {'door_number': '顺安路555-25', 'area': 74.74},
            '3-104': {'door_number': '顺安路555-27', 'area': 98.49},
            '3-105': {'door_number': '顺安路555-29', 'area': 98.37},
            '3-106': {'door_number': '顺安路555-31', 'area': 74.26},
            '8-3888': {'door_number': '七一东路3888', 'area': 356.98},
            '8-3888-1': {'door_number': '七一东路3888-1', 'area': 132.42},
            '8-3888-3': {'door_number': '七一东路3888-3', 'area': 259.14},
            '8-3888-5': {'door_number': '七一东路3888-5', 'area': 233.5},
            '8-3888-7': {'door_number': '七一东路3888-7', 'area': 233.5},
            '8-3888-9': {'door_number': '七一东路3888-9', 'area': 233.5},
            '8-3888-11': {'door_number': '七一东路3888-11', 'area': 233.5},
        }

        if shop_number in shop_data:
            data = shop_data[shop_number]
            # 计算物业费（每平米1.5元）
            property_fee = float(data['area']) * 1.5

            return JsonResponse({
                'success': True,
                'door_number': data['door_number'],
                'area': data['area'],
                'property_fee': round(property_fee, 2)
            })
        else:
            return JsonResponse({
                'success': False,
                'message': '未找到该商铺号的信息'
            })


class ExportShopView(LoginRequiredMixin, View):
    """导出商铺数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商铺列表"

        # 设置表头
        headers = [
            '商铺号', '门牌号', '租户姓名', '电话', '身份证号', '平米数',
            '起租时间', '物业费到期时间', '物业费用', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        shops = Shop.objects.all().order_by('shop_number')

        # 写入数据
        for row, shop in enumerate(shops, 2):
            ws.cell(row=row, column=1, value=shop.shop_number)
            ws.cell(row=row, column=2, value=shop.door_number or '')
            ws.cell(row=row, column=3, value=shop.tenant_name)
            ws.cell(row=row, column=4, value=shop.phone)
            ws.cell(row=row, column=5, value=shop.id_card)
            ws.cell(row=row, column=6, value=float(shop.area))
            ws.cell(row=row, column=7, value=shop.lease_start_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=8, value=shop.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=9, value=float(shop.property_fee))
            ws.cell(row=row, column=10, value=shop.get_status_display())
            ws.cell(row=row, column=11, value=shop.created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="商铺列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ImportShopView(LoginRequiredMixin, View):
    """导入商铺数据"""
    template_name = 'shops/import.html'

    def get(self, request):
        form = ShopImportForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = ShopImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:6]):  # 如果前6列都为空，跳过这行
                            continue

                        # 解析数据
                        shop_number = str(row[0]).strip() if row[0] else ''
                        door_number = str(row[1]).strip() if row[1] else ''
                        tenant_name = str(row[2]).strip() if row[2] else ''
                        phone = str(row[3]).strip() if row[3] else ''
                        id_card = str(row[4]).strip() if row[4] else ''

                        # 验证必填字段
                        if not shop_number:
                            errors.append(f'第{row_num}行: 商铺号不能为空')
                            error_count += 1
                            continue

                        if not tenant_name:
                            errors.append(f'第{row_num}行: 租户姓名不能为空')
                            error_count += 1
                            continue

                        # 解析数值字段
                        try:
                            area = Decimal(str(row[5])) if row[5] else Decimal('0')
                        except (ValueError, TypeError):
                            errors.append(f'第{row_num}行: 平米数格式错误')
                            error_count += 1
                            continue

                        # 解析日期
                        lease_start_date = row[6] if row[6] else timezone.now().date()
                        if isinstance(lease_start_date, str):
                            lease_start_date = datetime.strptime(lease_start_date, '%Y-%m-%d').date()

                        property_fee_due_date = row[7] if row[7] else timezone.now().date() + timedelta(days=365)
                        if isinstance(property_fee_due_date, str):
                            property_fee_due_date = datetime.strptime(property_fee_due_date, '%Y-%m-%d').date()

                        # 创建商铺记录
                        shop = Shop(
                            shop_number=shop_number,
                            door_number=door_number,
                            tenant_name=tenant_name,
                            phone=phone,
                            id_card=id_card,
                            area=area,
                            lease_start_date=lease_start_date,
                            property_fee_due_date=property_fee_due_date
                        )
                        shop.save()
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                if success_count > 0:
                    messages.success(request, f'成功导入 {success_count} 条商铺记录！')

                if error_count > 0:
                    error_msg = f'导入失败 {error_count} 条记录：\n' + '\n'.join(errors[:5])
                    if len(errors) > 5:
                        error_msg += f'\n... 还有 {len(errors) - 5} 个错误'
                    messages.error(request, error_msg)

                return redirect('shops:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})
