from django import forms
from .models import Shop, ShopPaymentHistory


class ShopForm(forms.ModelForm):
    """商铺表单"""
    
    class Meta:
        model = Shop
        fields = [
            'shop_number', 'door_number', 'tenant_name', 'phone', 'id_card', 
            'area', 'lease_start_date', 'property_fee_due_date'
        ]
        
        widgets = {
            'shop_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入商铺号',
                'id': 'id_shop_number'
            }),
            'door_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入门牌号',
                'id': 'id_door_number'
            }),
            'tenant_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入租户姓名'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入电话号码'
            }),
            'id_card': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入身份证号'
            }),
            'area': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '平米数',
                'step': '0.01',
                'id': 'id_area'
            }),
            'lease_start_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'property_fee_due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 根据商铺号自动填充门牌号和平米数
        if not self.instance.pk:  # 新增时
            self.fields['shop_number'].widget.attrs.update({
                'onchange': 'autoFillShopInfo(this.value)'
            })


class ShopSearchForm(forms.Form):
    """商铺搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索商铺号、租户姓名、电话...',
            'name': 'search'
        })
    )
    
    status = forms.ChoiceField(
        required=False,
        choices=[('', '全部状态')] + Shop.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )


class ShopPaymentForm(forms.ModelForm):
    """商铺缴费表单"""
    months = forms.IntegerField(
        label='缴费月数',
        min_value=1,
        max_value=24,
        initial=12,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入缴费月数'
        })
    )
    
    class Meta:
        model = ShopPaymentHistory
        fields = ['payment_method', 'notes']
        
        widgets = {
            'payment_method': forms.Select(
                choices=[
                    ('现金', '现金'),
                    ('银行转账', '银行转账'),
                    ('支付宝', '支付宝'),
                    ('微信', '微信'),
                    ('刷卡', '刷卡'),
                ],
                attrs={'class': 'form-select'}
            ),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '备注信息（可选）'
            }),
        }

    def __init__(self, shop=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        if shop:
            # 计算缴费金额
            monthly_fee = shop.calculate_property_fee()
            self.fields['amount'] = forms.DecimalField(
                label='缴费金额',
                initial=monthly_fee * 12,
                widget=forms.NumberInput(attrs={
                    'class': 'form-control',
                    'readonly': True
                })
            )


class ShopImportForm(forms.Form):
    """商铺导入表单"""
    file = forms.FileField(
        label='Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls'
        })
    )


class ShopPaymentHistorySearchForm(forms.Form):
    """商铺缴费流水搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索商铺号、租户姓名...'
        })
    )
    
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
