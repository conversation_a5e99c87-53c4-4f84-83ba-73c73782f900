{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>租客管理
                        <span class="badge bg-primary ms-2">{{ total_count }}户</span>
                        <span class="badge bg-success ms-1">{{ total_residents }}人</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'tenants:add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>新增租客
                                </a>
                                <a href="{% url 'tenants:import' %}" class="btn btn-success">
                                    <i class="fas fa-upload me-1"></i>批量导入
                                </a>
                                <a href="{% url 'tenants:export' %}" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>批量导出
                                </a>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary me-1">
                                        <i class="fas fa-search"></i>
                                    </button>
                                    <a href="{% url 'tenants:list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-redo"></i>
                                    </a>
                                </form>
                            </div>
                        </div>
                        

                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>楼号</th>
                                    <th>房号</th>
                                    <th>平米数</th>
                                    <th>租客姓名</th>
                                    <th>身份证号</th>
                                    <th>租客电话</th>
                                    <th>租住人数</th>
                                    <th>房东姓名</th>
                                    <th>房东电话</th>
                                    <th>入住时间</th>
                                    <th>物业费到期时间</th>
                                    <th>剩余天数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for tenant in tenants %}
                                <tr>
                                    <td>{{ tenant.building_number }}</td>
                                    <td>{{ tenant.room_number }}</td>
                                    <td>{{ tenant.area }}㎡</td>
                                    <td>{{ tenant.tenant_name }}</td>
                                    <td>{{ tenant.id_card }}</td>
                                    <td>{{ tenant.tenant_phone|default:"-" }}</td>
                                    <td>{{ tenant.resident_count }}人</td>
                                    <td>{{ tenant.landlord_name }}</td>
                                    <td>{{ tenant.landlord_phone }}</td>
                                    <td>{{ tenant.move_in_date }}</td>
                                    <td>{{ tenant.property_fee_due_date }}</td>
                                    <td>
                                        {% if tenant.days_until_due <= 7 %}
                                            <span class="badge bg-warning">{{ tenant.days_until_due }}天</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ tenant.days_until_due }}天</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'tenants:edit' tenant.pk %}" class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'tenants:renew' tenant.pk %}" class="btn btn-outline-success" title="续费">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                            <form method="post" action="{% url 'tenants:checkout_action' tenant.pk %}" style="display: inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-outline-warning" title="退房"
                                                        onclick="return confirm('确定要将此租客标记为退房吗？')">
                                                    <i class="fas fa-sign-out-alt"></i>
                                                </button>
                                            </form>
                                            <a href="{% url 'tenants:delete' tenant.pk %}" class="btn btn-outline-danger btn-delete"
                                               title="删除" data-message="确定要删除租客 {{ tenant.tenant_name }} 吗？">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="12" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无租客数据</p>
                                        <a href="{% url 'tenants:add' %}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>添加第一个租客
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除确认对话框
        var deleteButtons = document.querySelectorAll('.btn-delete');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                var message = this.getAttribute('data-message') || '确定要删除这条记录吗？';
                if (confirm(message)) {
                    window.location.href = this.href;
                }
            });
        });

        // 退房确认增强
        var checkoutForms = document.querySelectorAll('form[action*="checkout-action"]');
        checkoutForms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('确定要将此租客标记为退房吗？退房后租客将转入退房列表。')) {
                    this.submit();
                }
            });
        });
    });
</script>
{% endblock %}
