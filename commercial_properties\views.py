from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment
from io import BytesIO

from .models import CommercialProperty, CommercialPropertyPaymentHistory
from .forms import (CommercialPropertyForm, CommercialPropertySearchForm,
                   CommercialPropertyRenewForm, CommercialPropertyPaymentHistorySearchForm,
                   CommercialPropertyImportForm)


class CommercialPropertyListView(LoginRequiredMixin, ListView):
    """商品房列表视图"""
    model = CommercialProperty
    template_name = 'commercial_properties/list.html'
    context_object_name = 'properties'
    paginate_by = 20

    def get_queryset(self):
        # 自动更新逾期状态
        today = timezone.now().date()
        CommercialProperty.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 自动更新正常状态
        CommercialProperty.objects.filter(
            property_fee_due_date__gte=today,
            status='overdue'
        ).update(status='active')

        queryset = CommercialProperty.objects.filter(status__in=['active', 'overdue'])

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(owner_name__icontains=search) |
                Q(room_number__icontains=search) |
                Q(owner_phone__icontains=search) |
                Q(building_number__icontains=search)
            )

        # 楼号筛选
        building_number = self.request.GET.get('building_number')
        if building_number:
            queryset = queryset.filter(building_number__icontains=building_number)

        # 单元号筛选
        unit_number = self.request.GET.get('unit_number')
        if unit_number:
            queryset = queryset.filter(unit_number__icontains=unit_number)

        # 房号筛选
        room_number = self.request.GET.get('room_number')
        if room_number:
            queryset = queryset.filter(room_number__icontains=room_number)

        # 车位筛选
        has_parking = self.request.GET.get('has_parking')
        if has_parking == 'true':
            queryset = queryset.filter(has_parking=True)
        elif has_parking == 'false':
            queryset = queryset.filter(has_parking=False)

        return queryset.order_by('building_number', 'unit_number', 'room_number')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CommercialPropertySearchForm(self.request.GET)
        context['total_count'] = self.get_queryset().count()
        return context


class CommercialPropertyCreateView(LoginRequiredMixin, CreateView):
    """商品房创建视图"""
    model = CommercialProperty
    form_class = CommercialPropertyForm
    template_name = 'commercial_properties/form.html'
    success_url = reverse_lazy('commercial_properties:list')

    def form_valid(self, form):
        # 保存前计算物业费总额
        property = form.save(commit=False)
        property.property_fee_total = property.calculate_property_fee()
        property.save()

        messages.success(self.request, '商品房信息添加成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增商品房'
        return context


class CommercialPropertyUpdateView(LoginRequiredMixin, UpdateView):
    """商品房编辑视图"""
    model = CommercialProperty
    form_class = CommercialPropertyForm
    template_name = 'commercial_properties/form.html'
    success_url = reverse_lazy('commercial_properties:list')

    def form_valid(self, form):
        # 保存前重新计算物业费总额
        property = form.save(commit=False)
        property.property_fee_total = property.calculate_property_fee()
        property.save()

        messages.success(self.request, '商品房信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑商品房'
        return context


class CommercialPropertyDeleteView(LoginRequiredMixin, DeleteView):
    """商品房删除视图"""
    model = CommercialProperty
    success_url = reverse_lazy('commercial_properties:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '商品房信息删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueCommercialPropertyListView(LoginRequiredMixin, ListView):
    """逾期商品房列表视图"""
    model = CommercialProperty
    template_name = 'commercial_properties/overdue_list.html'
    context_object_name = 'properties'
    paginate_by = 20

    def get_queryset(self):
        # 更新逾期状态
        today = timezone.now().date()
        CommercialProperty.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        queryset = CommercialProperty.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(owner_name__icontains=search) |
                Q(room_number__icontains=search) |
                Q(owner_phone__icontains=search)
            )

        return queryset.order_by('property_fee_due_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CommercialPropertySearchForm(self.request.GET)
        return context


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """商品房缴费流水视图"""
    model = CommercialPropertyPaymentHistory
    template_name = 'commercial_properties/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = CommercialPropertyPaymentHistory.objects.select_related('property')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(property__owner_name__icontains=search) |
                Q(property__room_number__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            try:
                from datetime import datetime
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                start_datetime = timezone.make_aware(start_datetime, timezone.get_current_timezone())
                queryset = queryset.filter(payment_date__gte=start_datetime)
            except ValueError:
                pass

        end_date = self.request.GET.get('end_date')
        if end_date:
            try:
                from datetime import datetime, timedelta
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = timezone.make_aware(end_datetime, timezone.get_current_timezone())
                end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                queryset = queryset.filter(payment_date__lte=end_datetime)
            except ValueError:
                pass

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CommercialPropertyPaymentHistorySearchForm(self.request.GET)

        # 计算统计数据
        from decimal import Decimal
        from django.db.models import Sum, Count
        from datetime import timedelta

        queryset = self.get_queryset()

        if queryset.exists():
            # 总统计
            stats = queryset.aggregate(
                total_amount=Sum('amount'),
                total_count=Count('id')
            )

            total_amount = stats['total_amount'] or Decimal('0')
            total_count = stats['total_count'] or 0

            # 本月统计
            now = timezone.now()
            local_now = timezone.localtime(now)

            this_month_start = local_now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            next_month_start = this_month_start.replace(month=local_now.month + 1) if local_now.month < 12 else this_month_start.replace(year=local_now.year + 1, month=1)

            this_month_start_utc = timezone.make_aware(this_month_start.replace(tzinfo=None), timezone.get_current_timezone())
            next_month_start_utc = timezone.make_aware(next_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            this_month_stats = queryset.filter(
                payment_date__gte=this_month_start_utc,
                payment_date__lt=next_month_start_utc
            ).aggregate(
                count=Count('id'),
                amount=Sum('amount')
            )

            this_month_count = this_month_stats['count'] or 0
            this_month_amount = this_month_stats['amount'] or Decimal('0')

            # 上月统计
            last_month_start = this_month_start.replace(day=1) - timedelta(days=1)
            last_month_start = last_month_start.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start_utc = timezone.make_aware(last_month_start.replace(tzinfo=None), timezone.get_current_timezone())

            last_month_stats = queryset.filter(
                payment_date__gte=last_month_start_utc,
                payment_date__lt=this_month_start_utc
            ).aggregate(
                amount=Sum('amount')
            )

            last_month_amount = last_month_stats['amount'] or Decimal('0')
        else:
            total_amount = Decimal('0')
            this_month_count = 0
            this_month_amount = Decimal('0')
            last_month_amount = Decimal('0')
            total_count = 0

        context['total_amount'] = total_amount
        context['this_month_count'] = this_month_count
        context['this_month_amount'] = this_month_amount
        context['last_month_amount'] = last_month_amount
        context['total_count'] = total_count

        return context


class RenewCommercialPropertyView(LoginRequiredMixin, View):
    """商品房续费视图"""
    template_name = 'commercial_properties/renew.html'

    def get(self, request, pk):
        property = get_object_or_404(CommercialProperty, pk=pk)
        form = CommercialPropertyRenewForm(property=property)
        return render(request, self.template_name, {
            'property': property,
            'form': form
        })

    def post(self, request, pk):
        property = get_object_or_404(CommercialProperty, pk=pk)
        form = CommercialPropertyRenewForm(property=property, data=request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data['notes']

            # 计算续费金额（使用月费用）
            monthly_fee = property.calculate_monthly_property_fee()
            total_amount = monthly_fee * months

            # 计算新的到期日期（按月计算）
            current_due_date = property.property_fee_due_date

            # 使用dateutil的relativedelta来正确计算月份
            from dateutil.relativedelta import relativedelta

            if current_due_date < timezone.now().date():
                # 如果已经逾期，从今天开始计算
                start_date = timezone.now().date()
            else:
                # 从当前到期日期的下一天开始计算
                start_date = current_due_date + timedelta(days=1)

            # 计算新的到期日期
            new_due_date = start_date + relativedelta(months=months) - timedelta(days=1)

            # 更新商品房信息
            property.property_fee_due_date = new_due_date
            property.status = 'active'
            property.save()

            # 创建缴费记录
            CommercialPropertyPaymentHistory.objects.create(
                property=property,
                amount=total_amount,
                fee_start_date=current_due_date if current_due_date >= timezone.now().date() else timezone.now().date(),
                fee_end_date=new_due_date,
                payment_method=payment_method,
                notes=notes
            )

            messages.success(request, f'商品房 {property.owner_name} 续费成功！续费 {months} 个月，金额 ¥{total_amount}')
            return redirect('commercial_properties:list')

        return render(request, self.template_name, {
            'property': property,
            'form': form
        })


class ExportCommercialPropertyView(LoginRequiredMixin, View):
    """导出商品房数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商品房列表"

        # 设置表头
        headers = [
            '楼号', '单元号', '房号', '平米数', '业主姓名', '地下室', '车位',
            '业主电话', '物业费总额', '物业费到期日期', '楼层', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        properties = CommercialProperty.objects.all().order_by('building_number', 'unit_number', 'room_number')

        # 写入数据
        for row, property in enumerate(properties, 2):
            ws.cell(row=row, column=1, value=property.building_number)
            ws.cell(row=row, column=2, value=property.unit_number)
            ws.cell(row=row, column=3, value=property.room_number)
            ws.cell(row=row, column=4, value=float(property.area))
            ws.cell(row=row, column=5, value=property.owner_name)
            ws.cell(row=row, column=6, value='是' if property.has_basement else '否')
            ws.cell(row=row, column=7, value='是' if property.has_parking else '否')
            ws.cell(row=row, column=8, value=property.owner_phone)
            ws.cell(row=row, column=9, value=float(property.property_fee_total))
            ws.cell(row=row, column=10, value=property.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=11, value=property.floor)
            ws.cell(row=row, column=12, value=property.get_status_display())
            ws.cell(row=row, column=13, value=property.created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="商品房列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ImportCommercialPropertyView(LoginRequiredMixin, View):
    """导入商品房数据"""
    template_name = 'commercial_properties/import.html'

    def get(self, request):
        form = CommercialPropertyImportForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = CommercialPropertyImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:5]):  # 如果前5列都为空，跳过这行
                            continue

                        # 解析数据
                        building_number = str(row[0]) if row[0] else ''
                        unit_number = str(row[1]) if row[1] else ''
                        room_number = str(row[2]) if row[2] else ''
                        area = Decimal(str(row[3])) if row[3] else Decimal('0')
                        owner_name = str(row[4]) if row[4] else ''
                        has_basement = str(row[5]).lower() in ['是', 'true', '1'] if row[5] else False
                        has_parking = str(row[6]).lower() in ['是', 'true', '1'] if row[6] else False
                        owner_phone = str(row[7]) if row[7] else ''

                        # 解析日期
                        property_fee_due_date = row[9] if row[9] else timezone.now().date() + timedelta(days=365)
                        if isinstance(property_fee_due_date, str):
                            property_fee_due_date = datetime.strptime(property_fee_due_date, '%Y-%m-%d').date()

                        floor = int(row[10]) if row[10] else 1

                        # 创建商品房记录
                        property = CommercialProperty(
                            building_number=building_number,
                            unit_number=unit_number,
                            room_number=room_number,
                            area=area,
                            owner_name=owner_name,
                            has_basement=has_basement,
                            has_parking=has_parking,
                            owner_phone=owner_phone,
                            property_fee_due_date=property_fee_due_date,
                            floor=floor
                        )
                        property.save()
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                if success_count > 0:
                    messages.success(request, f'成功导入 {success_count} 条商品房记录！')

                if error_count > 0:
                    error_msg = f'导入失败 {error_count} 条记录：\n' + '\n'.join(errors[:5])
                    if len(errors) > 5:
                        error_msg += f'\n... 还有 {len(errors) - 5} 个错误'
                    messages.error(request, error_msg)

                return redirect('commercial_properties:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})


class BatchDeleteCommercialPropertyView(LoginRequiredMixin, View):
    """批量删除商品房"""

    def post(self, request):
        selected_items = request.POST.getlist('selected_items')

        if not selected_items:
            messages.error(request, '请选择要删除的商品房')
            return redirect('commercial_properties:list')

        try:
            # 获取要删除的商品房
            properties = CommercialProperty.objects.filter(id__in=selected_items)
            count = properties.count()

            if count == 0:
                messages.error(request, '没有找到要删除的商品房')
                return redirect('commercial_properties:list')

            # 执行删除
            properties.delete()

            messages.success(request, f'成功删除 {count} 个商品房')

        except Exception as e:
            messages.error(request, f'删除失败：{str(e)}')

        return redirect('commercial_properties:list')


class BatchDeletePaymentHistoryView(LoginRequiredMixin, View):
    """批量删除缴费记录"""

    def post(self, request):
        selected_items = request.POST.getlist('selected_items')

        if not selected_items:
            messages.error(request, '请选择要删除的缴费记录')
            return redirect('commercial_properties:payment_history')

        try:
            # 获取要删除的缴费记录
            payments = CommercialPropertyPaymentHistory.objects.filter(id__in=selected_items)
            count = payments.count()

            if count == 0:
                messages.error(request, '没有找到要删除的缴费记录')
                return redirect('commercial_properties:payment_history')

            # 执行删除
            payments.delete()

            messages.success(request, f'成功删除 {count} 条缴费记录')

        except Exception as e:
            messages.error(request, f'删除失败：{str(e)}')

        return redirect('commercial_properties:payment_history')


class ExportPaymentHistoryView(LoginRequiredMixin, View):
    """导出缴费流水数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "商品房缴费流水"

        # 设置表头
        headers = [
            '缴费时间', '业主姓名', '楼号', '单元号', '房号', '缴费金额',
            '费用开始日期', '费用结束日期', '缴费方式', '备注'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        payments = CommercialPropertyPaymentHistory.objects.select_related('property').order_by('-payment_date')

        # 写入数据
        for row, payment in enumerate(payments, 2):
            ws.cell(row=row, column=1, value=payment.payment_date.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=2, value=payment.property.owner_name)
            ws.cell(row=row, column=3, value=payment.property.building_number)
            ws.cell(row=row, column=4, value=payment.property.unit_number or '')
            ws.cell(row=row, column=5, value=payment.property.room_number)
            ws.cell(row=row, column=6, value=float(payment.amount))
            ws.cell(row=row, column=7, value=payment.fee_start_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=8, value=payment.fee_end_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=9, value=payment.payment_method)
            ws.cell(row=row, column=10, value=payment.notes or '')

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="商品房缴费流水_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response
