from django import forms
from django.utils import timezone
from datetime import datetime, timedelta
from .models import CommercialProperty, CommercialPropertyPaymentHistory


class CommercialPropertyForm(forms.ModelForm):
    """商品房表单"""
    
    class Meta:
        model = CommercialProperty
        fields = [
            'building_number', 'unit_number', 'room_number', 'area', 'floor',
            'owner_name', 'owner_phone', 'has_basement', 'basement_number',
            'has_parking', 'parking_number', 'property_fee_due_date'
        ]
        widgets = {
            'building_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入楼号'
            }),
            'unit_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入单元号（可选）'
            }),
            'room_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入房号'
            }),
            'area': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '平米数',
                'id': 'id_area'
            }),
            'floor': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '楼层',
                'id': 'id_floor'
            }),
            'owner_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入业主姓名'
            }),
            'owner_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入业主电话，多个号码用逗号分隔'
            }),
            'has_basement': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'id_has_basement'
            }),
            'basement_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入地下室号',
                'id': 'id_basement_number'
            }),
            'has_parking': forms.CheckboxInput(attrs={
                'class': 'form-check-input',
                'id': 'id_has_parking'
            }),
            'parking_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入车位号',
                'id': 'id_parking_number'
            }),
            'property_fee_due_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 设置默认日期
        if not self.instance.pk:
            # 默认物业费到期时间为一年后
            self.fields['property_fee_due_date'].initial = timezone.now().date() + timedelta(days=365)


class CommercialPropertySearchForm(forms.Form):
    """商品房搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索业主姓名、房号、电话...',
            'name': 'search'
        })
    )
    building_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '楼号'
        })
    )
    unit_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '单元号'
        })
    )
    room_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '房号'
        })
    )
    has_parking = forms.ChoiceField(
        required=False,
        choices=[('', '全部'), ('true', '有车位'), ('false', '无车位')],
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )


class CommercialPropertyRenewForm(forms.Form):
    """商品房续费表单"""
    months = forms.IntegerField(
        label='续费月数',
        min_value=1,
        max_value=24,
        initial=12,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入续费月数'
        })
    )
    payment_method = forms.CharField(
        label='缴费方式',
        initial='现金',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '缴费方式'
        })
    )
    notes = forms.CharField(
        label='备注',
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '备注信息'
        })
    )
    
    def __init__(self, property=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.property = property
        if property:
            # 计算续费金额（使用月费用）
            monthly_fee = property.calculate_monthly_property_fee()
            self.fields['amount'] = forms.DecimalField(
                label='续费金额',
                initial=monthly_fee * 12,
                widget=forms.NumberInput(attrs={
                    'class': 'form-control',
                    'readonly': True
                })
            )


class CommercialPropertyPaymentHistorySearchForm(forms.Form):
    """商品房缴费流水搜索表单"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '搜索业主姓名、房号...'
        })
    )
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class CommercialPropertyImportForm(forms.Form):
    """商品房导入表单"""
    file = forms.FileField(
        label='Excel文件',
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.xlsx,.xls'
        })
    )
    
    def clean_file(self):
        file = self.cleaned_data['file']
        if file:
            if not file.name.endswith(('.xlsx', '.xls')):
                raise forms.ValidationError('请上传Excel文件(.xlsx或.xls格式)')
        return file
