from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.contrib import messages
from django.urls import reverse_lazy, reverse
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import openpyxl
from openpyxl.styles import Font, Alignment
from io import BytesIO

from .models import Tenant, TenantPaymentHistory
from .forms import TenantForm, TenantSearchForm, TenantRenewForm, PaymentHistorySearchForm, TenantImportForm


class TenantListView(LoginRequiredMixin, ListView):
    """租客列表视图"""
    model = Tenant
    template_name = 'tenants/list.html'
    context_object_name = 'tenants'
    paginate_by = 20

    def get_queryset(self):
        # 先更新逾期状态
        today = timezone.now().date()
        Tenant.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        # 只显示正常状态的租客
        queryset = Tenant.objects.filter(status='active')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tenant_name__icontains=search) |
                Q(room_number__icontains=search) |
                Q(id_card__icontains=search) |
                Q(landlord_name__icontains=search)
            )

        # 楼号筛选
        building_number = self.request.GET.get('building_number')
        if building_number:
            queryset = queryset.filter(building_number=building_number)

        return queryset.order_by('building_number', 'room_number')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TenantSearchForm(self.request.GET)
        context['total_count'] = self.get_queryset().count()
        return context


class TenantCreateView(LoginRequiredMixin, CreateView):
    """租客创建视图"""
    model = Tenant
    form_class = TenantForm
    template_name = 'tenants/form.html'
    success_url = reverse_lazy('tenants:list')

    def form_valid(self, form):
        messages.success(self.request, '租客信息添加成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '新增租客'
        return context


class TenantUpdateView(LoginRequiredMixin, UpdateView):
    """租客编辑视图"""
    model = Tenant
    form_class = TenantForm
    template_name = 'tenants/form.html'
    success_url = reverse_lazy('tenants:list')

    def form_valid(self, form):
        messages.success(self.request, '租客信息更新成功！')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑租客'
        return context


class TenantDeleteView(LoginRequiredMixin, DeleteView):
    """租客删除视图"""
    model = Tenant
    success_url = reverse_lazy('tenants:list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, '租客信息删除成功！')
        return super().delete(request, *args, **kwargs)


class OverdueTenantListView(LoginRequiredMixin, ListView):
    """逾期租客列表视图"""
    model = Tenant
    template_name = 'tenants/overdue_list.html'
    context_object_name = 'tenants'
    paginate_by = 20

    def get_queryset(self):
        # 更新逾期状态
        today = timezone.now().date()
        Tenant.objects.filter(
            property_fee_due_date__lt=today,
            status='active'
        ).update(status='overdue')

        queryset = Tenant.objects.filter(status='overdue')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tenant_name__icontains=search) |
                Q(room_number__icontains=search) |
                Q(landlord_phone__icontains=search)
            )

        return queryset.order_by('property_fee_due_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TenantSearchForm(self.request.GET)
        return context


class CheckoutTenantListView(LoginRequiredMixin, ListView):
    """退房租客列表视图"""
    model = Tenant
    template_name = 'tenants/checkout_list.html'
    context_object_name = 'tenants'
    paginate_by = 20

    def get_queryset(self):
        queryset = Tenant.objects.filter(status='checkout')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tenant_name__icontains=search) |
                Q(room_number__icontains=search)
            )

        return queryset.order_by('-updated_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = TenantSearchForm(self.request.GET)
        return context


class PaymentHistoryListView(LoginRequiredMixin, ListView):
    """租客缴费流水视图"""
    model = TenantPaymentHistory
    template_name = 'tenants/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        queryset = TenantPaymentHistory.objects.select_related('tenant')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(tenant__tenant_name__icontains=search) |
                Q(tenant__room_number__icontains=search)
            )

        # 日期筛选
        start_date = self.request.GET.get('start_date')
        if start_date:
            queryset = queryset.filter(payment_date__gte=start_date)

        end_date = self.request.GET.get('end_date')
        if end_date:
            queryset = queryset.filter(payment_date__lte=end_date)

        return queryset.order_by('-payment_date')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = PaymentHistorySearchForm(self.request.GET)
        return context


class RenewTenantView(LoginRequiredMixin, View):
    """租客续费视图"""
    template_name = 'tenants/renew.html'

    def get(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        form = TenantRenewForm(tenant=tenant)
        return render(request, self.template_name, {
            'tenant': tenant,
            'form': form
        })

    def post(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        form = TenantRenewForm(tenant=tenant, data=request.POST)

        if form.is_valid():
            months = form.cleaned_data['months']
            payment_method = form.cleaned_data['payment_method']
            notes = form.cleaned_data['notes']

            # 计算续费金额
            monthly_fee = tenant.property_fee
            total_amount = monthly_fee * months

            # 计算新的到期日期
            current_due_date = tenant.property_fee_due_date
            if current_due_date < timezone.now().date():
                # 如果已经逾期，从今天开始计算
                new_due_date = timezone.now().date() + timedelta(days=30 * months)
            else:
                # 从当前到期日期开始计算
                new_due_date = current_due_date + timedelta(days=30 * months)

            # 更新租客信息
            tenant.property_fee_due_date = new_due_date
            tenant.status = 'active'
            tenant.save()

            # 创建缴费记录
            TenantPaymentHistory.objects.create(
                tenant=tenant,
                amount=total_amount,
                fee_start_date=current_due_date if current_due_date >= timezone.now().date() else timezone.now().date(),
                fee_end_date=new_due_date,
                payment_method=payment_method,
                notes=notes
            )

            messages.success(request, f'租客 {tenant.tenant_name} 续费成功！续费 {months} 个月，金额 ¥{total_amount}')
            return redirect('tenants:list')

        return render(request, self.template_name, {
            'tenant': tenant,
            'form': form
        })


class CheckoutTenantView(LoginRequiredMixin, View):
    """租客退房视图"""

    def post(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        tenant.status = 'checkout'
        tenant.save()

        messages.success(request, f'租客 {tenant.tenant_name} 已成功退房！')
        return redirect('tenants:list')


class RestoreTenantView(LoginRequiredMixin, View):
    """恢复租客视图"""

    def post(self, request, pk):
        tenant = get_object_or_404(Tenant, pk=pk)
        tenant.status = 'active'
        tenant.save()

        messages.success(request, f'租客 {tenant.tenant_name} 已成功恢复！')
        return redirect('tenants:checkout')


class BatchDeleteCheckoutView(LoginRequiredMixin, View):
    """批量删除退房租客"""

    def post(self, request):
        selected_items = request.POST.getlist('selected_items')
        if selected_items:
            deleted_count = Tenant.objects.filter(
                pk__in=selected_items,
                status='checkout'
            ).delete()[0]

            messages.success(request, f'成功删除 {deleted_count} 个退房租客！')
        else:
            messages.warning(request, '请选择要删除的租客！')

        return redirect('tenants:checkout')


class ExportTenantView(LoginRequiredMixin, View):
    """导出租客数据"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "租客列表"

        # 设置表头
        headers = [
            '楼号', '房号', '平米数', '租客姓名', '身份证号', '租住人数',
            '房东姓名', '房东电话', '入住时间', '物业费到期时间', '物业费用',
            '楼层', '状态', '创建时间'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 获取数据
        tenants = Tenant.objects.all().order_by('building_number', 'room_number')

        # 写入数据
        for row, tenant in enumerate(tenants, 2):
            ws.cell(row=row, column=1, value=tenant.building_number)
            ws.cell(row=row, column=2, value=tenant.room_number)
            ws.cell(row=row, column=3, value=float(tenant.area))
            ws.cell(row=row, column=4, value=tenant.tenant_name)
            ws.cell(row=row, column=5, value=tenant.id_card)
            ws.cell(row=row, column=6, value=tenant.resident_count)
            ws.cell(row=row, column=7, value=tenant.landlord_name)
            ws.cell(row=row, column=8, value=tenant.landlord_phone)
            ws.cell(row=row, column=9, value=tenant.move_in_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=10, value=tenant.property_fee_due_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=11, value=float(tenant.property_fee))
            ws.cell(row=row, column=12, value=tenant.floor)
            ws.cell(row=row, column=13, value=tenant.get_status_display())
            ws.cell(row=row, column=14, value=tenant.created_at.strftime('%Y-%m-%d %H:%M:%S'))

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="租客列表_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # 保存到响应
        wb.save(response)
        return response


class ImportTenantView(LoginRequiredMixin, View):
    """导入租客数据"""
    template_name = 'tenants/import.html'

    def get(self, request):
        form = TenantImportForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        form = TenantImportForm(request.POST, request.FILES)

        if form.is_valid():
            file = form.cleaned_data['file']
            skip_duplicates = request.POST.get('skip_duplicates') == 'on'

            try:
                # 读取Excel文件
                wb = openpyxl.load_workbook(file)
                ws = wb.active

                success_count = 0
                error_count = 0
                skip_count = 0
                errors = []

                # 跳过表头，从第二行开始读取
                for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                    try:
                        if not any(row[:4]):  # 如果前4列都为空，跳过这行
                            continue

                        # 解析数据
                        building_number = str(row[0]) if row[0] else ''
                        room_number = str(row[1]) if row[1] else ''
                        area = Decimal(str(row[2])) if row[2] else Decimal('0')
                        tenant_name = str(row[3]) if row[3] else ''
                        id_card = str(row[4]) if row[4] else ''
                        resident_count = int(row[5]) if row[5] else 1
                        landlord_name = str(row[6]) if row[6] else ''
                        landlord_phone = str(row[7]) if row[7] else ''

                        # 解析日期
                        move_in_date = row[8] if row[8] else timezone.now().date()
                        if isinstance(move_in_date, str):
                            move_in_date = datetime.strptime(move_in_date, '%Y-%m-%d').date()

                        property_fee_due_date = row[9] if row[9] else timezone.now().date() + timedelta(days=365)
                        if isinstance(property_fee_due_date, str):
                            property_fee_due_date = datetime.strptime(property_fee_due_date, '%Y-%m-%d').date()

                        floor = int(row[10]) if len(row) > 10 and row[10] else 1

                        # 检查重复记录
                        if skip_duplicates:
                            existing = Tenant.objects.filter(
                                building_number=building_number,
                                room_number=room_number
                            ).exists()
                            if existing:
                                skip_count += 1
                                continue

                        # 根据房号自动设置面积
                        if room_number:
                            last_two = room_number[-2:] if len(room_number) >= 2 else ''
                            if last_two in ['01', '04']:
                                area = Decimal('130.00')
                            elif last_two in ['02', '03']:
                                area = Decimal('90.00')

                        # 创建租客记录
                        tenant = Tenant(
                            building_number=building_number,
                            room_number=room_number,
                            area=area,
                            tenant_name=tenant_name,
                            id_card=id_card,
                            resident_count=resident_count,
                            landlord_name=landlord_name,
                            landlord_phone=landlord_phone,
                            move_in_date=move_in_date,
                            property_fee_due_date=property_fee_due_date,
                            floor=floor
                        )
                        tenant.save()
                        success_count += 1

                    except Exception as e:
                        error_count += 1
                        errors.append(f'第{row_num}行: {str(e)}')

                # 显示导入结果
                result_messages = []
                if success_count > 0:
                    result_messages.append(f'成功导入 {success_count} 条记录')
                if skip_count > 0:
                    result_messages.append(f'跳过重复记录 {skip_count} 条')
                if error_count > 0:
                    result_messages.append(f'导入失败 {error_count} 条记录')

                if success_count > 0:
                    messages.success(request, '；'.join(result_messages) + '！')
                elif skip_count > 0:
                    messages.warning(request, '；'.join(result_messages) + '！')

                if error_count > 0:
                    error_msg = '导入错误详情：\n' + '\n'.join(errors[:5])
                    if len(errors) > 5:
                        error_msg += f'\n... 还有 {len(errors) - 5} 个错误'
                    messages.error(request, error_msg)

                return redirect('tenants:list')

            except Exception as e:
                messages.error(request, f'文件处理失败：{str(e)}')

        return render(request, self.template_name, {'form': form})


class DownloadTemplateView(LoginRequiredMixin, View):
    """下载导入模板"""

    def get(self, request):
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "租客导入模板"

        # 设置表头
        headers = [
            '楼号', '房号', '平米数', '租客姓名', '身份证号', '租住人数',
            '房东姓名', '房东电话', '入住时间', '物业费到期时间', '楼层'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 添加示例数据
        example_data = [
            ['1', '101', '130', '张三', '110101199001011234', '2', '李房东', '13800138000', '2024-01-01', '2024-12-31', '1'],
            ['1', '102', '90', '李四', '110101199002021234', '1', '王房东', '13900139000', '2024-02-01', '2025-01-31', '1'],
        ]

        for row_num, row_data in enumerate(example_data, 2):
            for col_num, value in enumerate(row_data, 1):
                ws.cell(row=row_num, column=col_num, value=value)

        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 20)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="租客导入模板.xlsx"'

        # 保存到响应
        wb.save(response)
        return response
