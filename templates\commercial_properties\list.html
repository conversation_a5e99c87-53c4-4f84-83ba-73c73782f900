{% extends 'base/base.html' %}
{% load static %}

{% block title %}商品房物业费列表 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-home me-2"></i>商品房物业费管理
                        <span class="badge bg-success ms-2">正常 {{ total_count }}套</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-8">
                                <a href="{% url 'commercial_properties:add' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>新增商品房
                                </a>
                                <a href="{% url 'commercial_properties:import' %}" class="btn btn-success">
                                    <i class="fas fa-upload me-1"></i>批量导入
                                </a>
                                <a href="{% url 'commercial_properties:export' %}" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>批量导出
                                </a>
                                <button type="button" class="btn btn-danger batch-action" id="batch-delete" disabled>
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                            <div class="col-md-4">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- 筛选条件 -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <form method="get" class="row g-3">
                                    <div class="col-md-2">
                                        <label class="form-label">楼号</label>
                                        {{ search_form.building_number }}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">单元号</label>
                                        {{ search_form.unit_number }}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">房号</label>
                                        {{ search_form.room_number }}
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">车位</label>
                                        {{ search_form.has_parking }}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">筛选</button>
                                            <a href="{% url 'commercial_properties:list' %}" class="btn btn-outline-secondary">重置</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                    <th>楼号</th>
                                    <th>单元号</th>
                                    <th>房号</th>
                                    <th>平米数</th>
                                    <th>业主姓名</th>
                                    <th>地下室</th>
                                    <th>车位</th>
                                    <th>业主电话</th>
                                    <th>物业费总额</th>
                                    <th>物业费到期日期</th>
                                    <th>剩余天数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for property in properties %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input item-checkbox" value="{{ property.pk }}">
                                    </td>
                                    <td>{{ property.building_number }}</td>
                                    <td>{{ property.unit_number|default:"-" }}</td>
                                    <td>{{ property.room_number }}</td>
                                    <td>{{ property.area }}㎡</td>
                                    <td>{{ property.owner_name }}</td>
                                    <td>
                                        {% if property.has_basement %}
                                            <span class="badge bg-info">{{ property.basement_number|default:"有" }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">无</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if property.has_parking %}
                                            <span class="badge bg-success">{{ property.parking_number|default:"有" }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">无</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ property.owner_phone }}</td>
                                    <td class="text-success fw-bold">¥{{ property.calculate_property_fee }}</td>
                                    <td>{{ property.property_fee_due_date }}</td>
                                    <td>
                                        {% if property.days_until_due <= 7 %}
                                            <span class="badge bg-warning">{{ property.days_until_due }}天</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ property.days_until_due }}天</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'commercial_properties:edit' property.pk %}" class="btn btn-outline-primary" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'commercial_properties:renew' property.pk %}" class="btn btn-outline-success" title="续费">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                            <a href="{% url 'commercial_properties:delete' property.pk %}" class="btn btn-outline-danger btn-delete" 
                                               title="删除" data-message="确定要删除商品房 {{ property.owner_name }} 吗？">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                        <p class="text-muted">暂无正常状态的商品房</p>
                                        <div>
                                            <a href="{% url 'commercial_properties:add' %}" class="btn btn-primary me-2">
                                                <i class="fas fa-plus me-1"></i>添加商品房
                                            </a>
                                            <a href="{% url 'commercial_properties:overdue' %}" class="btn btn-outline-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>查看到期列表
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 费用统计 -->
                    {% if properties %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="fas fa-chart-bar me-2"></i>费用统计
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>总房屋数：</strong>{{ properties|length }}套
                                    </div>
                                    <div class="col-md-3">
                                        <strong>有车位：</strong>
                                        {% with parking_count=0 %}
                                            {% for property in properties %}
                                                {% if property.has_parking %}
                                                    {% with parking_count=parking_count|add:1 %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                            {{ parking_count }}套
                                        {% endwith %}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>有地下室：</strong>
                                        {% with basement_count=0 %}
                                            {% for property in properties %}
                                                {% if property.has_basement %}
                                                    {% with basement_count=basement_count|add:1 %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                            {{ basement_count }}套
                                        {% endwith %}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>月收入总额：</strong>
                                        ¥{% with total_income=0 %}
                                            {% for property in properties %}
                                                {% with total_income=total_income|add:property.property_fee_total %}{% endwith %}
                                            {% endfor %}
                                            {{ total_income }}
                                        {% endwith %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 批量删除功能
        const batchDeleteBtn = document.getElementById('batch-delete');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要删除的商品房');
                    return false;
                }

                const result = confirm(`⚠️ 警告：您即将删除 ${checkedBoxes.length} 个商品房！\n\n此操作将永久删除选中的商品房记录，无法恢复！\n\n请慎重考虑，确定要继续吗？`);

                if (result === true) {
                    // 创建表单提交批量删除
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '/commercial-properties/batch-delete/';
                    form.style.display = 'none';

                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    checkedBoxes.forEach(function(checkbox) {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'selected_items';
                        input.value = checkbox.value;
                        form.appendChild(input);
                    });

                    document.body.appendChild(form);
                    form.submit();
                } else {
                    // 用户点击取消，取消选中状态
                    checkedBoxes.forEach(function(checkbox) {
                        checkbox.checked = false;
                    });
                    updateBatchButtons();
                    updateSelectAllCheckbox();
                }
                return false;
            });
        }
    });
</script>
{% endblock %}
