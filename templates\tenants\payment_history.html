{% extends 'base/base.html' %}
{% load static %}

{% block title %}租客物业费流水统计 - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-line me-2 text-success"></i>租客物业费流水统计
                        <span class="badge bg-success ms-2">{{ payments|length }}</span>
                    </h4>
                </div>
                
                <div class="card-body">
                    <!-- 统计概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card bg-primary text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-number" id="total-amount">
                                    ¥{{ total_amount|floatformat:2 }}
                                </div>
                                <div class="stats-label">总收入金额</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-success text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <div class="stats-number">{{ payments|length }}</div>
                                <div class="stats-label">缴费笔数</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-info text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-calendar-month"></i>
                                </div>
                                <div class="stats-number" id="this-month-count">
                                    {{ this_month_count }}
                                </div>
                                <div class="stats-label">本月缴费</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card bg-warning text-white">
                                <div class="stats-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="stats-number" id="average-amount">
                                    ¥{{ average_amount|floatformat:2 }}
                                </div>
                                <div class="stats-label">平均金额</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 搜索和筛选 -->
                    <div class="toolbar">
                        <div class="row">
                            <div class="col-md-6">
                                <a href="{% url 'tenants:export_payment_history' %}" class="btn btn-info">
                                    <i class="fas fa-download me-1"></i>导出流水数据
                                </a>
                                <button type="button" class="btn btn-danger" id="batch-delete" disabled>
                                    <i class="fas fa-trash me-1"></i>批量删除
                                </button>
                            </div>
                            <div class="col-md-6">
                                <form method="get" class="d-flex">
                                    <div class="search-box flex-grow-1 me-2">
                                        <i class="fas fa-search search-icon"></i>
                                        {{ search_form.search }}
                                    </div>
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- 快捷日期筛选 -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="btn-group me-3" role="group">
                                    <button type="button" class="btn btn-outline-secondary btn-sm date-filter" data-days="0">今天</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm date-filter" data-days="1">昨天</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm date-filter" data-days="3">近3天</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm date-filter" data-days="7">近7天</button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm date-filter" data-period="month">本月</button>
                                </div>
                                <a href="{% url 'tenants:payment_history' %}" class="btn btn-outline-secondary btn-sm">全部</a>
                            </div>
                        </div>

                        <!-- 自定义日期筛选 -->
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <form method="get" class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">开始日期</label>
                                        {{ search_form.start_date }}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">结束日期</label>
                                        {{ search_form.end_date }}
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div>
                                            <button type="submit" class="btn btn-primary">筛选</button>
                                            <a href="{% url 'tenants:payment_history' %}" class="btn btn-outline-secondary">重置</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="select-all" class="form-check-input">
                                    </th>
                                    <th>缴费时间</th>
                                    <th>租客姓名</th>
                                    <th>房号</th>
                                    <th>缴费金额</th>
                                    <th>费用期间</th>
                                    <th>缴费方式</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input item-checkbox" value="{{ payment.pk }}">
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ payment.payment_date|date:"Y-m-d H:i" }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="status-indicator status-active"></div>
                                            {{ payment.tenant.tenant_name }}
                                        </div>
                                    </td>
                                    <td>{{ payment.tenant.building_number }}-{{ payment.tenant.room_number }}</td>
                                    <td class="text-success fw-bold">¥{{ payment.amount }}</td>
                                    <td>
                                        <small class="text-muted">
                                            {{ payment.fee_start_date }} 至 {{ payment.fee_end_date }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ payment.payment_method }}</span>
                                    </td>
                                    <td>
                                        {% if payment.notes %}
                                            <span class="text-muted">{{ payment.notes|truncatechars:30 }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">暂无缴费记录</p>
                                        <a href="{% url 'tenants:list' %}" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>查看租客列表
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 月度统计图表 -->
                    {% if payments %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>月度收入趋势
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="monthlyChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="分页导航" class="mt-4">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">首页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">上一页</a>
                                </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页</span>
                            </li>
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">下一页</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.start_date %}&start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}&end_date={{ request.GET.end_date }}{% endif %}">末页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-radius: 15px;
        padding: 1.5rem;
        position: relative;
        overflow: hidden;
        margin-bottom: 1rem;
    }
    
    .stats-icon {
        position: absolute;
        right: 1rem;
        top: 1rem;
        font-size: 2rem;
        opacity: 0.3;
    }
    
    .stats-number {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 快捷日期筛选
        const dateFilterButtons = document.querySelectorAll('.date-filter');
        dateFilterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const days = this.getAttribute('data-days');
                const period = this.getAttribute('data-period');

                let url = new URL(window.location.href);
                url.searchParams.delete('start_date');
                url.searchParams.delete('end_date');

                const today = new Date();

                if (period === 'month') {
                    // 本月
                    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    url.searchParams.set('start_date', firstDay.toISOString().split('T')[0]);
                    url.searchParams.set('end_date', lastDay.toISOString().split('T')[0]);
                } else if (days) {
                    if (days === '0') {
                        // 今天
                        url.searchParams.set('start_date', today.toISOString().split('T')[0]);
                        url.searchParams.set('end_date', today.toISOString().split('T')[0]);
                    } else if (days === '1') {
                        // 昨天
                        const yesterday = new Date(today);
                        yesterday.setDate(yesterday.getDate() - 1);
                        url.searchParams.set('start_date', yesterday.toISOString().split('T')[0]);
                        url.searchParams.set('end_date', yesterday.toISOString().split('T')[0]);
                    } else {
                        // 近N天
                        const startDate = new Date(today);
                        startDate.setDate(startDate.getDate() - parseInt(days) + 1);
                        url.searchParams.set('start_date', startDate.toISOString().split('T')[0]);
                        url.searchParams.set('end_date', today.toISOString().split('T')[0]);
                    }
                }

                window.location.href = url.toString();
            });
        });

        // 全选功能
        const selectAllCheckbox = document.getElementById('select-all');
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        const batchDeleteBtn = document.getElementById('batch-delete');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                updateBatchButtons();
            });
        }

        itemCheckboxes.forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                updateBatchButtons();
                updateSelectAllCheckbox();
            });
        });

        function updateBatchButtons() {
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
        }

        function updateSelectAllCheckbox() {
            const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');

            if (checkedBoxes.length === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedBoxes.length === itemCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }

        // 批量删除
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.item-checkbox:checked');
                if (checkedBoxes.length === 0) {
                    alert('请选择要删除的缴费记录');
                    return;
                }

                const result = confirm(`⚠️ 警告：您即将删除 ${checkedBoxes.length} 条缴费记录！\n\n此操作将永久删除选中的缴费记录，无法恢复！\n\n请慎重考虑，确定要继续吗？`);

                if (result) {
                    // 实现批量删除逻辑
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '/tenants/batch-delete-payment/';

                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    checkedBoxes.forEach(function(checkbox) {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'selected_items';
                        input.value = checkbox.value;
                        form.appendChild(input);
                    });

                    document.body.appendChild(form);
                    form.submit();
                } else {
                    // 用户点击取消，不做任何操作
                    return false;
                }
            });
        }

        // 月度收入图表
        const ctx = document.getElementById('monthlyChart');
        if (ctx) {
            // 这里可以添加图表数据处理逻辑
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '月度收入',
                        data: [12000, 15000, 13000, 18000, 16000, 20000],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: '租客物业费收入趋势'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>
{% endblock %}
