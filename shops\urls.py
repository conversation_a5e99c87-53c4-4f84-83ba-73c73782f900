from django.urls import path
from . import views

app_name = 'shops'

urlpatterns = [
    path('', views.ShopListView.as_view(), name='list'),
    path('add/', views.ShopCreateView.as_view(), name='add'),
    path('<int:pk>/edit/', views.ShopUpdateView.as_view(), name='edit'),
    path('<int:pk>/delete/', views.ShopDeleteView.as_view(), name='delete'),
    path('overdue/', views.OverdueShopListView.as_view(), name='overdue'),
    path('checkout/', views.CheckoutShopListView.as_view(), name='checkout'),
    path('payment-history/', views.PaymentHistoryListView.as_view(), name='payment_history'),
    path('<int:pk>/renew/', views.ShopRenewView.as_view(), name='renew'),
    path('<int:pk>/checkout-action/', views.CheckoutShopView.as_view(), name='checkout_action'),
    path('export/', views.ExportShopView.as_view(), name='export'),
    path('import/', views.ImportShopView.as_view(), name='import'),
    path('api/shop-info/', views.GetShopInfoView.as_view(), name='shop_info'),
    path('test/', views.TestShopView.as_view(), name='test'),
]
