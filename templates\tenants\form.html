{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - 东悦物业管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>{{ title }}
                    </h4>
                </div>
                
                <div class="card-body">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <!-- 房屋信息 -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-home me-2"></i>房屋信息
                                </h5>
                                
                                <div class="mb-3">
                                    <label for="{{ form.building_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-building me-1"></i>楼号 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.building_number }}
                                    {% if form.building_number.errors %}
                                        <div class="text-danger small">{{ form.building_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.room_number.id_for_label }}" class="form-label">
                                        <i class="fas fa-door-open me-1"></i>房号 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.room_number }}
                                    <div class="form-text">输入房号后会自动计算平米数</div>
                                    {% if form.room_number.errors %}
                                        <div class="text-danger small">{{ form.room_number.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.area.id_for_label }}" class="form-label">
                                                <i class="fas fa-ruler-combined me-1"></i>平米数 <span class="text-danger">*</span>
                                            </label>
                                            {{ form.area }}
                                            {% if form.area.errors %}
                                                <div class="text-danger small">{{ form.area.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="{{ form.floor.id_for_label }}" class="form-label">
                                                <i class="fas fa-layer-group me-1"></i>楼层 <span class="text-danger">*</span>
                                            </label>
                                            {{ form.floor }}
                                            {% if form.floor.errors %}
                                                <div class="text-danger small">{{ form.floor.errors.0 }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-calculator me-1"></i>物业费用（自动计算）
                                    </label>
                                    <input type="text" class="form-control" id="calculated_fee" readonly>
                                    <div class="form-text">
                                        计算规则：基础费1元/㎡ + 电梯费(≤11层:0.3元/㎡, >11层:0.35元/㎡)
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 租客信息 -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user me-2"></i>租客信息
                                </h5>
                                
                                <div class="mb-3">
                                    <label for="{{ form.tenant_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-1"></i>租客姓名 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.tenant_name }}
                                    {% if form.tenant_name.errors %}
                                        <div class="text-danger small">{{ form.tenant_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.id_card.id_for_label }}" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>身份证号 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.id_card }}
                                    {% if form.id_card.errors %}
                                        <div class="text-danger small">{{ form.id_card.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.tenant_phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-mobile-alt me-1"></i>租客电话 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.tenant_phone }}
                                    {% if form.tenant_phone.errors %}
                                        <div class="text-danger small">{{ form.tenant_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.resident_count.id_for_label }}" class="form-label">
                                        <i class="fas fa-users me-1"></i>租住人数
                                    </label>
                                    {{ form.resident_count }}
                                    {% if form.resident_count.errors %}
                                        <div class="text-danger small">{{ form.resident_count.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.landlord_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>房东姓名 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.landlord_name }}
                                    {% if form.landlord_name.errors %}
                                        <div class="text-danger small">{{ form.landlord_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.landlord_phone.id_for_label }}" class="form-label">
                                        <i class="fas fa-phone me-1"></i>房东电话 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.landlord_phone }}
                                    {% if form.landlord_phone.errors %}
                                        <div class="text-danger small">{{ form.landlord_phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 时间信息 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-calendar me-2"></i>时间信息
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.move_in_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-plus me-1"></i>入住时间 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.move_in_date }}
                                    {% if form.move_in_date.errors %}
                                        <div class="text-danger small">{{ form.move_in_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.property_fee_due_date.id_for_label }}" class="form-label">
                                        <i class="fas fa-calendar-times me-1"></i>物业费到期时间 <span class="text-danger">*</span>
                                    </label>
                                    {{ form.property_fee_due_date }}
                                    {% if form.property_fee_due_date.errors %}
                                        <div class="text-danger small">{{ form.property_fee_due_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{% url 'tenants:list' %}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times me-1"></i>取消
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>保存
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const roomNumberInput = document.getElementById('id_room_number');
        const areaInput = document.getElementById('id_area');
        const floorInput = document.getElementById('id_floor');
        const calculatedFeeInput = document.getElementById('calculated_fee');
        
        // 房号变化时自动设置平米数
        if (roomNumberInput && areaInput) {
            roomNumberInput.addEventListener('input', function() {
                const roomNumber = this.value;
                if (roomNumber.length >= 2) {
                    const lastTwo = roomNumber.slice(-2);
                    if (lastTwo === '01' || lastTwo === '04') {
                        areaInput.value = '130.00';
                    } else if (lastTwo === '02' || lastTwo === '03') {
                        areaInput.value = '90.00';
                    }
                    calculatePropertyFee();
                }
            });
        }
        
        // 计算物业费
        function calculatePropertyFee() {
            const area = parseFloat(areaInput.value) || 0;
            const floor = parseInt(floorInput.value) || 1;
            
            if (area > 0) {
                const baseFee = area * 1.0; // 基础费用1元/㎡
                const elevatorFee = floor <= 11 ? area * 0.3 : area * 0.35; // 电梯费
                const totalFee = baseFee + elevatorFee;
                
                calculatedFeeInput.value = '¥' + totalFee.toFixed(2);
            }
        }
        
        // 楼层或面积变化时重新计算
        if (floorInput) {
            floorInput.addEventListener('input', calculatePropertyFee);
        }
        
        if (areaInput) {
            areaInput.addEventListener('input', calculatePropertyFee);
        }
        
        // 页面加载时计算一次
        calculatePropertyFee();

        // 设置日期字段的值（编辑时）
        {% if object %}
        const moveInDateInput = document.getElementById('id_move_in_date');
        const propertyFeeDueDateInput = document.getElementById('id_property_fee_due_date');

        if (moveInDateInput && '{{ object.move_in_date|date:"Y-m-d" }}') {
            moveInDateInput.value = '{{ object.move_in_date|date:"Y-m-d" }}';
        }

        if (propertyFeeDueDateInput && '{{ object.property_fee_due_date|date:"Y-m-d" }}') {
            propertyFeeDueDateInput.value = '{{ object.property_fee_due_date|date:"Y-m-d" }}';
        }
        {% endif %}
    });
</script>
{% endblock %}
